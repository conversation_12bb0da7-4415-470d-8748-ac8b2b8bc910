import numpy as np
import scipy.sparse as sp
from pypower.idx_bus import PD  # 添加PD常量导入

class DCPowerFlow:
    """
    独立的DC潮流计算类，不依赖PYPOWER
    """
    def __init__(self, verbose=False, wind_power_bus=None):
        """
        初始化DC潮流计算器

        Args:
            verbose: 是否输出详细信息
            wind_power_bus: 风电注入的母线索引 (0-based)，None表示不启用风电
        """
        self.verbose = verbose
        self.baseMVA = 100.0  # 默认基准功率，单位MVA
        self.buses = None     # 母线信息
        self.branches = None  # 线路信息
        self.generators = None  # 发电机信息
        self.loads = None     # 负荷信息
        self.wind_power_bus = wind_power_bus  # 风电注入节点
        self.wind_power = 0.0  # 风电出力
        
        # 节点导纳矩阵
        self.B = None
        
        # 结果存储
        self.voltage_angles = None  # 节点电压相角
        self.branch_flows = None    # 线路功率流
        self.gen_dispatch = None    # 发电机出力
        self.ref_bus = None         # 参考节点
        
        # 平衡机相关设置
        self.slack_gen_idx = None    # 平衡机发电机索引（默认无）
        self.slack_gen_enabled = False  # 是否启用平衡机功能
        
        # 初始化功率不平衡相关属性
        self.residual_imbalance = 0.0
        self.adjustment_results = {
            'initial_imbalance': 0.0,
            'adjustments': [],
            'final_imbalance': 0.0
        }
        self.current_segment_imbalance = 0.0

    def load_case(self, buses, branches, generators, gencost=None):
        """
        加载电力系统数据
        
        Args:
            buses: 母线数据
            branches: 线路数据
            generators: 发电机数据
            gencost: 发电机成本系数（可选）
        """
        # 保存原始数据的深拷贝
        self.buses = buses.copy()
        self.branches = branches.copy()
        self.generators = generators.copy()
        
        # 设置参考节点（使用第一个发电机所连接的节点）
        self.ref_bus = int(generators[0, 0]) - 1  # 转换为0基索引
        
        # 自动找出容量最大的发电机作为平衡机
        self.find_largest_generator()
        
        # 保存负荷数据
        self.loads = buses[:, PD].copy()  # 从母线数据中获取负荷
        
        # 保存发电机成本系数
        if gencost is not None:
            self.gen_cost_a = gencost[:, 4].copy()
            self.gen_cost_b = gencost[:, 5].copy()
            self.gen_cost_c = gencost[:, 6].copy()
            
        if self.verbose:
            print(f"已加载电力系统模型:")
            print(f"  母线数量: {len(self.buses)}")
            print(f"  线路数量: {len(self.branches)}")
            print(f"  发电机数量: {len(self.generators)}")
            print(f"  参考节点: {self.ref_bus + 1}")
            if self.slack_gen_enabled:
                print(f"  平衡机: 发电机 {self.slack_gen_idx + 1} (容量: {self.generators[self.slack_gen_idx][8]:.2f}MW)")
            
        return self
    
    def find_largest_generator(self):
        """
        自动找出容量最大的发电机作为平衡机
        """
        if self.generators is None or len(self.generators) == 0:
            return
            
        max_capacity = -1
        max_idx = 0
        
        # 遍历所有发电机，找出容量最大的
        for i, gen in enumerate(self.generators):
            capacity = gen[8]  # 第8列是发电机最大出力
            if capacity > max_capacity:
                max_capacity = capacity
                max_idx = i
                
        # 设置找到的最大容量发电机为平衡机
        self.set_slack_generator(max_idx)
        
        if self.verbose:
            print(f"已自动选择容量最大的发电机(G{max_idx+1}, 容量:{max_capacity}MW)作为平衡机")
    
    def set_slack_generator(self, gen_idx):
        """
        设置指定的发电机为平衡机
        
        Args:
            gen_idx: 平衡机发电机的索引（从0开始）
        """
        if gen_idx is None:
            self.slack_gen_enabled = False
            self.slack_gen_idx = None
            return self
            
        if not hasattr(self, 'generators') or self.generators is None:
            raise ValueError("必须先加载系统数据再设置平衡机")
            
        if gen_idx >= len(self.generators):
            raise ValueError(f"发电机索引 {gen_idx} 超出范围，系统中只有 {len(self.generators)} 台发电机")
            
        self.slack_gen_idx = gen_idx
        self.slack_gen_enabled = True
        
        if self.verbose:
            print(f"已将发电机 {gen_idx + 1} 设置为平衡机")
            
        return self
        
    def optimize_initial_dispatch(self):
        """
        优化初始发电机调度，基于电网拓扑结构和负荷分布，确保合理分配和预留足够的调节余量
        
        新算法考虑了以下因素：
        1. 基于拓扑结构的负荷分配：根据发电机所在节点及相邻节点的负荷需求分配出力
        2. 节点连接度优化：连接度高的节点获得更多初始出力
        3. 确保出力在限制范围内
        4. 总出力平衡调整：确保总发电量等于总负荷
        """
        # 计算总负荷和系统容量
        total_load = np.sum(self.loads)
        total_capacity = sum(gen[8] for gen in self.generators)  # 总装机容量
        total_min_output = sum(gen[9] for gen in self.generators)  # 最小出力总和
        
        # 检查负荷是否在系统容量范围内
        if total_load > total_capacity:
            if self.verbose:
                print(f"警告: 负荷 ({total_load:.2f} MW) 超过总容量 ({total_capacity:.2f} MW)")
            total_load = total_capacity  # 限制负荷不超过总容量
        elif total_load < total_min_output:
            if self.verbose:
                print(f"警告: 负荷 ({total_load:.2f} MW) 低于最小出力和 ({total_min_output:.2f} MW)")
            total_load = total_min_output  # 确保至少达到最小出力和
        
        # 初始化所有机组出力为最小值
        initial_outputs = np.zeros(len(self.generators))
        for i in range(len(self.generators)):
            initial_outputs[i] = self.generators[i][9]  # 设置为最小出力
        
        # 计算当前出力总和和需要分配的容量
        current_total = sum(initial_outputs)
        remaining_load = total_load - current_total
        
        # 1. 构建电网拓扑结构
        nb = len(self.buses)     # 节点数量
        nl = len(self.branches)  # 线路数量
        
        # 创建节点连接关系图
        adjacency_list = [[] for _ in range(nb)]
        node_connections = [0] * nb  # 记录每个节点的连接度
        
        for i in range(nl):
            f_bus = int(self.branches[i, 0]) - 1  # 起始节点（0基索引）
            t_bus = int(self.branches[i, 1]) - 1  # 终止节点（0基索引）
            
            # 添加双向连接
            adjacency_list[f_bus].append(t_bus)
            adjacency_list[t_bus].append(f_bus)
            
            # 更新连接度
            node_connections[f_bus] += 1
            node_connections[t_bus] += 1
        
        # 2. 识别发电机所在节点及相邻节点
        gen_nodes = []  # 发电机所在节点列表
        gen_to_node = {}  # 发电机索引到节点的映射
        node_to_gens = {}  # 节点到发电机索引的映射
        
        for i, gen in enumerate(self.generators):
            bus_idx = int(gen[0]) - 1  # 发电机所在节点（0基索引）
            gen_nodes.append(bus_idx)
            gen_to_node[i] = bus_idx
            
            if bus_idx not in node_to_gens:
                node_to_gens[bus_idx] = []
            node_to_gens[bus_idx].append(i)
        
        # 3. 计算每个发电机的"本地负荷"（所在节点及相邻节点的负荷总和）
        local_loads = []
        for i in range(len(self.generators)):
            node = gen_to_node[i]
            local_load = self.loads[node]  # 发电机所在节点的负荷
            
            # 添加相邻节点的负荷
            for neighbor in adjacency_list[node]:
                local_load += self.loads[neighbor]
            
            local_loads.append(local_load)
        
        # 计算总本地负荷
        total_local_load = sum(local_loads)
        if total_local_load <= 0:
            # 如果总本地负荷为零，使用均匀分配
            local_load_ratios = [1.0 / len(self.generators)] * len(self.generators)
        else:
            # 计算每个发电机的本地负荷占比
            local_load_ratios = [load / total_local_load for load in local_loads]
        
        # 4. 应用节点连接度优化
        connection_factors = []
        max_connections = max(node_connections) if node_connections else 1
        
        for i in range(len(self.generators)):
            node = gen_to_node[i]
            # 连接度高的节点获得额外加成（最多20%）
            factor = 1.0 + 0.2 * (node_connections[node] / max_connections)
            connection_factors.append(factor)
        
        # 归一化连接度因子
        total_factor = sum(connection_factors)
        connection_factors = [factor / total_factor for factor in connection_factors]
        
        # 5. 结合本地负荷比例和连接度因子，计算初步分配比例
        # 本地负荷占70%权重，连接度占30%权重
        allocation_ratios = []
        for i in range(len(self.generators)):
            ratio = 0.7 * local_load_ratios[i] + 0.3 * connection_factors[i]
            
            # 如果是平衡机，增加其初始分配比例，给予平衡机更多的出力
            if self.slack_gen_enabled and i == self.slack_gen_idx:
                # 给平衡机增加50%的分配比例，让其有更多的下调余量
                ratio = ratio * 1.5
                if self.verbose:
                    print(f"为平衡机(G{i+1})增加50%的初始分配比例")
                    
            allocation_ratios.append(ratio)
        
        # 归一化分配比例
        total_ratio = sum(allocation_ratios)
        allocation_ratios = [ratio / total_ratio for ratio in allocation_ratios]
        
        # 6. 根据分配比例分配负荷
        available_capacity = []
        for i in range(len(self.generators)):
            pg_min = self.generators[i][9]
            pg_max = self.generators[i][8]
            available = pg_max - pg_min
            
            # 判断是否是平衡机
            is_slack = False
            if self.slack_gen_enabled:
                is_slack = i == self.slack_gen_idx
                
            available_capacity.append({
                'idx': i,
                'available': available,
                'min': pg_min,
                'max': pg_max,
                'ratio': allocation_ratios[i],
                'is_slack': is_slack
            })
        
        # 初步分配负荷
        for gen in available_capacity:
            i = gen['idx']
            allocation = remaining_load * gen['ratio']
            
            # 确保不超过可用容量
            allocation = min(allocation, gen['available'])
            
            # 更新初始出力
            initial_outputs[i] += allocation
        
        # 7. 总出力平衡调整
        total_gen = np.sum(initial_outputs)
        imbalance = total_load - total_gen
        
        if abs(imbalance) > 0.1:
            # 如果仍有不平衡，按比例调整
            if imbalance > 0:  # 需要增加发电
                # 计算剩余可用容量
                remaining_capacity = []
                for i in range(len(self.generators)):
                    remaining = self.generators[i][8] - initial_outputs[i]
                    if remaining > 0:
                        # 为平衡机增加额外的优先级
                        is_slack = self.slack_gen_enabled and i == self.slack_gen_idx
                        
                        # 如果是平衡机，给予其更高的容量权重
                        effective_capacity = self.generators[i][8]
                        if is_slack:
                            # 增加平衡机的有效容量权重，使其在排序时有更高优先级
                            effective_capacity = self.generators[i][8] * 1.5
                            if self.verbose:
                                print(f"为平衡机(G{i+1})提高调整优先级")
                                
                        remaining_capacity.append({
                            'idx': i,
                            'remaining': remaining,
                            'capacity': effective_capacity,  # 使用修改后的有效容量进行排序
                            'is_slack': is_slack
                        })
                
                # 按容量大小排序（优先调整大容量发电机和平衡机）
                remaining_capacity.sort(key=lambda x: x['capacity'], reverse=True)
                
                # 逐个调整直到平衡
                for gen in remaining_capacity:
                    i = gen['idx']
                    adjustment = min(gen['remaining'], imbalance)
                    initial_outputs[i] += adjustment
                    imbalance -= adjustment
                    
                    if imbalance <= 0.1:
                        break
                        
            else:  # 需要减少发电
                # 计算可减少的容量
                reducible_capacity = []
                for i in range(len(self.generators)):
                    reducible = initial_outputs[i] - self.generators[i][9]
                    if reducible > 0:
                        reducible_capacity.append({
                            'idx': i,
                            'reducible': reducible,
                            'capacity': self.generators[i][8]  # 用于排序的总容量
                        })
                
                # 按容量大小排序（优先调整大容量发电机）
                reducible_capacity.sort(key=lambda x: x['capacity'], reverse=True)
                
                # 逐个调整直到平衡
                for gen in reducible_capacity:
                    i = gen['idx']
                    adjustment = min(gen['reducible'], -imbalance)
                    initial_outputs[i] -= adjustment
                    imbalance += adjustment
                    
                    if imbalance >= -0.1:
                        break
        
        # 8. 为平衡机预留额外的下调容量（通过增加其出力）
        if self.slack_gen_enabled:
            # 获取平衡机索引
            slack_idx = self.slack_gen_idx
            slack_pg_min = self.generators[slack_idx][9]
            slack_pg_max = self.generators[slack_idx][8]
            slack_pg_current = initial_outputs[slack_idx]
            
            # 计算平衡机的上调余量
            slack_up_margin = slack_pg_max - slack_pg_current
            
            # 如果有足够的上调余量，给平衡机分配额外的出力（预留下调空间）
            if slack_up_margin > 0:
                # 将平衡机出力增加到至少为最小出力与最大出力之间的60%位置
                target_output = slack_pg_min + (slack_pg_max - slack_pg_min) * 0.6
                
                # 确保目标出力不低于当前出力
                target_output = max(target_output, slack_pg_current)
                
                # 调整平衡机出力
                if target_output > slack_pg_current:
                    additional_output = target_output - slack_pg_current
                    initial_outputs[slack_idx] = target_output
                    
                    if self.verbose:
                        print(f"为平衡机(G{slack_idx+1})增加{additional_output:.2f}MW出力，以提供更多下调余量")
        
        # 9. 最终检查并确保所有出力在限制范围内
        for i in range(len(self.generators)):
            initial_outputs[i] = max(self.generators[i][9], min(self.generators[i][8], initial_outputs[i]))
        
        # 更新发电机出力
        for i in range(len(self.generators)):
            self.generators[i][1] = initial_outputs[i]
        
        # 验证最终总发电量是否等于总负荷
        final_total_gen = np.sum(initial_outputs)
        final_imbalance = total_load - final_total_gen
        
        if self.verbose:
            print(f"\n已优化发电机初始调度 (基于拓扑结构):")
            print(f"总负荷: {total_load:.2f} MW")
            print(f"总发电: {final_total_gen:.2f} MW")
            print(f"功率不平衡: {final_imbalance:.2f} MW")
            
            total_up_margin = 0
            total_down_margin = 0
            for i, pg in enumerate(initial_outputs):
                node = gen_to_node[i]
                margin_up = self.generators[i][8] - pg
                margin_down = pg - self.generators[i][9]
                # 计算爬坡限制
                ramp_rate = self.generators[i][8] * (0.25 if i < 2 else 0.30 if i < 4 else 0.35)
                total_up_margin += margin_up
                total_down_margin += margin_down
                
                # 显示平衡机标记和节点连接度
                ref_mark = ""
                if self.slack_gen_enabled and i == self.slack_gen_idx:
                    ref_mark = "[平衡机]"
                
                print(f"  G{i+1}@节点{node+1}: {pg:.2f}MW {ref_mark} (连接度: {node_connections[node]}, 上调余量: {margin_up:.2f}MW, 下调余量: {margin_down:.2f}MW)")
            
            print(f"  系统总上调余量: {total_up_margin:.2f}MW, 总下调余量: {total_down_margin:.2f}MW")
            
            # 计算经济性指标
            total_cost = 0
            for i, pg in enumerate(initial_outputs):
                cost = self.gen_cost_a[i] * pg * pg + self.gen_cost_b[i] * pg + self.gen_cost_c[i]
                total_cost += cost
            print(f"  总发电成本: {total_cost:.2f}$/h")
            
        return initial_outputs
    
    def build_matrices(self):
        """构建系统矩阵"""
        nb = len(self.buses)     # 节点数量
        nl = len(self.branches)  # 线路数量
        
        # 初始化矩阵
        self.B = np.zeros((nb, nb))  # 节点导纳矩阵
        
        # 构建节点导纳矩阵B
        for i in range(nl):
            f_bus = int(self.branches[i, 0]) - 1  # 起始节点
            t_bus = int(self.branches[i, 1]) - 1  # 终止节点
            x = self.branches[i, 3]               # 支路电抗
            
            # 设置最小电抗值以避免数值不稳定
            if abs(x) < 1e-6:
                x = np.sign(x) * 1e-6 if x != 0 else 1e-6
                
            # 更新导纳矩阵
            self.B[f_bus, f_bus] += 1/x
            self.B[t_bus, t_bus] += 1/x
            self.B[f_bus, t_bus] -= 1/x
            self.B[t_bus, f_bus] -= 1/x
            
        return self
    
    def set_wind_power(self, power):
        """设置风电出力"""
        if self.wind_power_bus is not None:
            self.wind_power = power
        else:
            self.wind_power = 0.0

    def set_gen_dispatch(self, pg_values=None):
        """
        设置发电机出力
        
        Args:
            pg_values: 发电机出力列表，如果为None则使用当前值
        """
        if pg_values is not None:
            if len(pg_values) != len(self.generators):
                raise ValueError(f"发电机出力数量 {len(pg_values)} 与发电机数量 {len(self.generators)} 不匹配")
                
            # 更新发电机出力
            for i, pg in enumerate(pg_values):
                self.generators[i][1] = pg
                
        # 保存当前发电机出力
        self.gen_dispatch = np.array([gen[1] for gen in self.generators])
        
        return self
    
    def run_dc_power_flow(self, prev_outputs=None):
        """
        运行DC潮流计算，直接使用智能体设置的发电机出力
        
        Args:
            prev_outputs: 上一时刻的发电机出力，用于检查爬坡约束
            
        Returns:
            True 如果潮流计算成功，False 如果失败
        """
        if self.B is None:
            self.build_matrices()
            
        nb = len(self.buses)     # 节点数量
        nl = len(self.branches)  # 线路数量
        
        try:
            # 1. 构建节点功率注入向量（考虑基准功率）
            p_injection = np.zeros(nb)
            
            # 添加发电机功率注入（转换为标幺值）
            for gen in self.generators:
                bus_idx = int(gen[0]) - 1  # 转换为0基索引
                p_injection[bus_idx] += gen[1] / self.baseMVA

            # 添加风电功率注入（如果启用）
            if self.wind_power_bus is not None and self.wind_power > 0:
                p_injection[self.wind_power_bus] += self.wind_power / self.baseMVA

            # 减去负荷功率（转换为标幺值）
            for i, load in enumerate(self.loads):
                p_injection[i] -= load / self.baseMVA
                
            # 2. 计算初始功率平衡
            initial_power_balance = np.sum(p_injection)
            initial_imbalance = initial_power_balance * self.baseMVA
            
            # 初始化当前时间段不平衡
            self.current_segment_imbalance = initial_imbalance
            
            # 重置残余不平衡量，确保每个时间步只考虑当前时间步的不平衡量
            self.residual_imbalance = initial_imbalance
            
            # 记录初始状态
            self.adjustment_results = {
                'initial_imbalance': initial_imbalance,
                'adjustments': [],
                'final_imbalance': initial_imbalance
            }
                
            # 3. 如果启用平衡机，调整平衡机出力来平衡系统
            if self.slack_gen_enabled and abs(initial_imbalance) > 1e-3:
                # 清晰打印平衡机调节前的信息
                if self.verbose:
                    print(f"\n┌───────────────────────── 平衡机调节信息 ─────────────────────────┐")
                    print(f"│ 初始功率不平衡: {initial_imbalance:.2f} MW")
                    total_gen = sum(gen[1] for gen in self.generators)
                    total_load = sum(self.loads)
                    # 总发电量包含风电
                    total_gen_with_wind = total_gen + self.wind_power
                    print(f"│ 火电发电量: {total_gen:.2f} MW, 风电出力: {self.wind_power:.2f} MW")
                    print(f"│ 总发电量: {total_gen_with_wind:.2f} MW, 总负荷: {total_load:.2f} MW")
                    if self.slack_gen_idx is not None:
                        slack_idx = self.slack_gen_idx
                        print(f"│ 平衡机(G{slack_idx+1})当前出力: {self.generators[slack_idx][1]:.2f} MW")
                        print(f"│ 平衡机上调余量: {self.generators[slack_idx][8] - self.generators[slack_idx][1]:.2f} MW")
                        print(f"│ 平衡机下调余量: {self.generators[slack_idx][1] - self.generators[slack_idx][9]:.2f} MW")
                
                # 执行平衡机调节
                self._adjust_slack_generators()
                # current_segment_imbalance 已经在_adjust_slack_generators()中更新
                
                # 重新计算功率注入向量和不平衡
                p_injection = np.zeros(nb)
                for gen in self.generators:
                    bus_idx = int(gen[0]) - 1
                    p_injection[bus_idx] += gen[1] / self.baseMVA

                # 添加风电功率注入（如果启用）- 修复：之前遗漏了这部分
                if self.wind_power_bus is not None and self.wind_power > 0:
                    p_injection[self.wind_power_bus] += self.wind_power / self.baseMVA

                for i, load in enumerate(self.loads):
                    p_injection[i] -= load / self.baseMVA
                
                # 计算最终功率平衡
                final_power_balance = np.sum(p_injection)
                final_imbalance = final_power_balance * self.baseMVA

                ##################################################################
                # 如果仍有微小残余不平衡(数值误差或爬坡限制导致)，
                # 在进入潮流求解前做最后一次校正，以免 B_red 奇异。
                ##################################################################
                balance_threshold = 1e-6  # 标幺值阈值 (≈0.1MW)
                if abs(final_power_balance) > balance_threshold:
                    if self.slack_gen_enabled and self.slack_gen_idx is not None:
                        # 直接把残余全部交给平衡机
                        slack_idx = self.slack_gen_idx
                        self.generators[slack_idx][1] -= final_power_balance * self.baseMVA
                        bus_idx = int(self.generators[slack_idx][0]) - 1
                        p_injection[bus_idx] -= final_power_balance
                    else:
                        # 无平衡机时，平均分配到所有机组
                        correction_per_gen = (final_power_balance * self.baseMVA) / len(self.generators)
                        for gen in self.generators:
                            gen[1] -= correction_per_gen
                        # 重新计算注入向量
                        p_injection = np.zeros(nb)
                        for gen in self.generators:
                            bus_idx = int(gen[0]) - 1
                            p_injection[bus_idx] += gen[1] / self.baseMVA
                        for i, load in enumerate(self.loads):
                            p_injection[i] -= load / self.baseMVA

                    # 再次更新功率平衡量
                    final_power_balance = np.sum(p_injection)
                    final_imbalance = final_power_balance * self.baseMVA

                # 更新最终不平衡记录
                self.adjustment_results['final_imbalance'] = final_imbalance
                # 更新当前时间段的不平衡量，但不会影响下一个时间步的初始不平衡量
                self.residual_imbalance = final_imbalance
                
                # 清晰打印平衡机调节后的信息
                if self.verbose:
                    print(f"│ 调节后功率不平衡: {final_imbalance:.2f} MW")
                    if self.slack_gen_idx is not None:
                        slack_idx = self.slack_gen_idx
                        print(f"│ 平衡机(G{slack_idx+1})调节后出力: {self.generators[slack_idx][1]:.2f} MW")
                    # 如果还有剩余不平衡，打印警告
                    if abs(final_imbalance) > 0.5:
                        print(f"│ ⚠️ 警告: 平衡机调节后仍有{final_imbalance:.2f} MW不平衡")
                    print(f"└────────────────────────────────────────────────────────────────┘")
            
            # 4. 计算节点电压相角
            # 移除参考节点的方程
            mask = np.ones(nb, dtype=bool)
            mask[self.ref_bus] = False
            
            B_red = self.B[mask][:, mask]
            p_red = p_injection[mask]
            
            # 求解相角
            theta_red = np.linalg.solve(B_red, p_red)
            
            # 插入参考节点的相角(为0)
            theta = np.zeros(nb)
            j = 0
            for i in range(nb):
                if i != self.ref_bus:
                    theta[i] = theta_red[j]
                    j += 1
                    
            # 5. 计算线路功率流
            branch_flows = np.zeros(nl)
            for i, branch in enumerate(self.branches):
                f_bus = int(branch[0]) - 1  # 转换为0基索引
                t_bus = int(branch[1]) - 1  # 转换为0基索引
                x = branch[3]    # 电抗
                
                # 避免分母为零，同时保持符号
                if abs(x) < 1e-6:
                    x = np.sign(x) * 1e-6 if x != 0 else 1e-6
                    
                # 计算功率流（转换回实际功率）
                branch_flows[i] = (theta[f_bus] - theta[t_bus]) / x * self.baseMVA
                
            # 6. 保存结果
            self.voltage_angles = theta
            self.branch_flows = branch_flows
                
            return True
            
        except Exception as e:
            if self.verbose:
                print(f"DC潮流计算失败: {str(e)}")
            return False
    
    def _adjust_slack_generators(self):
        """
        调整平衡机出力以达到功率平衡
        如果平衡机达到出力限制，按照等耗量微增率准则、爬坡率和出力限制由其他发电机分担剩余的不平衡量
        """
        MAX_ITERATIONS = 3  # 最大调整轮数
        BALANCE_THRESHOLD = 1e-3  # 平衡阈值

        # 需要调整的功率不平衡量 (正值表示系统发电过剩，负值表示系统发电不足)
        imbalance = self.residual_imbalance
        
        # 调整输出日志，不需要额外判断不平衡量大小
        should_log = self.verbose
        
        # 记录调整历史
        adjustment_history = []
        
        # 1. 首先尝试使用平衡机调节
        idx = self.slack_gen_idx
        current_pg = self.generators[idx][1]
        pg_min = self.generators[idx][9]
        pg_max = self.generators[idx][8]
        
        # 计算平衡机的爬坡限制 (平衡机可以上调或下调多少)
        ramp_rate = pg_max * 0.25  # G1的爬坡率为25%
        
        # 根据不平衡方向计算调整量
        # 明确功率不平衡量的正负与平衡机调节方向之间的关系：
        # - imbalance > 0: 系统发电过剩，需要减少发电（负调整）
        # - imbalance < 0: 系统发电不足，需要增加发电（正调整）
        #
        # 注意：adjustment 的符号表示发电机出力的变化方向
        # - 正值表示增加发电
        # - 负值表示减少发电
        if imbalance > 0:  # 系统发电过剩，需要减少发电
            max_possible_adjustment = min(current_pg - pg_min, ramp_rate)  # 平衡机最多能减少多少
            adjustment = -min(max_possible_adjustment, imbalance)  # 负值，表示减少发电
        else:  # 系统发电不足，需要增加发电
            max_possible_adjustment = min(pg_max - current_pg, ramp_rate)  # 平衡机最多能增加多少
            adjustment = min(max_possible_adjustment, abs(imbalance))  # 正值，表示增加发电
        
        # 更新平衡机出力
        new_pg = current_pg + adjustment
        
        # 记录平衡机调整
        adjustment_history.append({
            'generator': f'G{idx+1}',
            'from': current_pg,
            'to': new_pg,
            'adjustment': adjustment  # 正值表示增加，负值表示减少
        })
        
        # 检查是否超过发电机限制 (不应该发生，因为已经考虑了限制，但出于安全考虑仍然检查)
        if new_pg < pg_min:
            new_pg = pg_min
            if should_log:
                print(f"│ ⚠️ 平衡机G{idx+1}达到最小出力限制: {pg_min}MW")
        elif new_pg > pg_max:
            new_pg = pg_max
            if should_log:
                print(f"│ ⚠️ 平衡机G{idx+1}达到最大出力限制: {pg_max}MW")
        
        # 更新平衡机出力
        self.generators[idx][1] = new_pg
        if should_log:
            # 使用表格形式输出平衡机调整信息
            adjustment_text = f"{adjustment:.2f}MW" if adjustment != 0 else "无调整"
            adjustment_direction = "⬆️ 增加" if adjustment > 0 else "⬇️ 减少" if adjustment < 0 else "➖ 无变化"
            print(f"│ 平衡机调整: G{idx+1} {current_pg:.2f}MW → {new_pg:.2f}MW ({adjustment_direction}: {abs(adjustment):.2f}MW)")
        
        # 计算剩余不平衡量
        # 注意：平衡机的调整会直接抵消部分不平衡
        # 如果平衡机增加发电(正adjustment)，会减少"发电不足"(负imbalance)
        # 如果平衡机减少发电(负adjustment)，会减少"发电过剩"(正imbalance)
        remaining_imbalance = imbalance + adjustment  # adjustment已经带有正负号
        
        # 将当前时间段的不平衡记录下来
        self.current_segment_imbalance = remaining_imbalance
        
        # 2. 如果平衡机无法完全消除不平衡，进行多轮调整
        # 确保每个时间步的调整是独立的，不受上一时间步的影响
        iteration = 0
        while abs(remaining_imbalance) > BALANCE_THRESHOLD and iteration < MAX_ITERATIONS:
            iteration += 1
            if should_log:
                print(f"│ 平衡机无法完全平衡，启动第{iteration}轮其他发电机调整")
                print(f"│ 剩余功率不平衡: {remaining_imbalance:.2f}MW")
                print(f"├─────────────────── 其他发电机调整 ─────────────────────")
            
            # 收集所有非平衡机的信息
            non_slack_gens = []
            for i in range(len(self.generators)):
                if i == idx:  # 跳过平衡机
                    continue
                
                curr_pg = self.generators[i][1]
                pg_min = self.generators[i][9]
                pg_max = self.generators[i][8]
                
                # 计算该机组的爬坡限制
                if i < 2:
                    ramp_rate = 0.25
                elif i < 4:
                    ramp_rate = 0.30
                else:
                    ramp_rate = 0.35
                
                # 严格遵守各发电机的爬坡限制
                
                ramp_limit = pg_max * ramp_rate
                
                # 计算考虑爬坡限制和机组限制的实际可调容量
                if remaining_imbalance > 0:  # 需要减少发电
                    physical_limit = curr_pg - pg_min
                    available_capacity = min(physical_limit, ramp_limit)
                else:  # 需要增加发电
                    physical_limit = pg_max - curr_pg
                    available_capacity = min(physical_limit, ramp_limit)
                
                # 计算当前出力下的增量成本
                incremental_cost = 2 * self.gen_cost_a[i] * curr_pg + self.gen_cost_b[i]
                
                # 只要有微小的调节能力，就加入可调节发电机列表
                if available_capacity > 0.01:  # 降低门槛，只要有0.01MW以上的调节能力就参与
                    non_slack_gens.append({
                        'idx': i,
                        'pg': curr_pg,
                        'pg_min': pg_min,
                        'pg_max': pg_max,
                        'available_capacity': available_capacity,
                        'ramp_limit': ramp_limit,
                        'incremental_cost': incremental_cost
                    })
            
            if not non_slack_gens:
                if should_log:
                    print("│ ⚠️ 警告: 所有发电机均无调节余量，无法消除功率不平衡")
                break
            
            # 计算总可调容量
            total_available = sum(gen['available_capacity'] for gen in non_slack_gens)
            
            if total_available < abs(remaining_imbalance) and should_log:
                print(f"│ ⚠️ 警告: 总可调容量({total_available:.2f}MW)不足以消除不平衡({remaining_imbalance:.2f}MW)")
            
            # 按照等耗量微增率准则排序
            if remaining_imbalance > 0:  # 需要减少发电
                non_slack_gens.sort(key=lambda x: x['incremental_cost'], reverse=True)  # 减发时优先减少高增量成本的机组
            else:  # 需要增加发电
                non_slack_gens.sort(key=lambda x: x['incremental_cost'])  # 增发时优先使用低增量成本的机组
            
            # 计算总权重
            total_weight = 0
            for gen in non_slack_gens:
                if remaining_imbalance < 0:  # 需要增加发电
                    gen['weight'] = 1.0 / max(gen['incremental_cost'], 0.01)  # 避免除以零
                else:  # 需要减少发电
                    gen['weight'] = gen['incremental_cost']
                total_weight += gen['weight']
            
            # 按照权重比例分配调整量
            unallocated = abs(remaining_imbalance)
            total_adjustment = 0.0
            
            if should_log and len(non_slack_gens) > 0:
                print(f"│ {'发电机':<5} │ {'原出力':>10} │ {'新出力':>10} │ {'调整量':>10} │")
                print(f"│ {'─'*5} │ {'─'*10} │ {'─'*10} │ {'─'*10} │")
            
            for gen in non_slack_gens:
                # 按权重计算理想分配量
                target_share = (gen['weight'] / total_weight) * unallocated
                actual_capacity = min(target_share, gen['available_capacity'])
                
                # 根据不平衡方向确定调整方向
                # 明确功率不平衡量的正负与非平衡机调节方向之间的关系：
                # - remaining_imbalance > 0: 系统发电过剩，需要减少发电（负调整）
                # - remaining_imbalance < 0: 系统发电不足，需要增加发电（正调整）
                #
                # adjustment的符号: 正=增加发电，负=减少发电
                if remaining_imbalance > 0:  # 需要减少发电
                    adjustment = -actual_capacity  # 负值表示减少发电
                else:  # 需要增加发电
                    adjustment = actual_capacity  # 正值表示增加发电
                
                # 更新发电机出力
                i = gen['idx']
                curr_pg = gen['pg']
                new_pg = curr_pg + adjustment
                
                # 确保在限制范围内
                new_pg = max(gen['pg_min'], min(gen['pg_max'], new_pg))
                
                # 重新计算实际调整量
                actual_adjustment = new_pg - curr_pg
                total_adjustment += actual_adjustment
                
                # 更新发电机出力
                self.generators[i][1] = new_pg
                
                # 记录调整历史
                adjustment_history.append({
                    'generator': f'G{i+1}',
                    'from': curr_pg,
                    'to': new_pg,
                    'adjustment': actual_adjustment  # 正值表示增加，负值表示减少
                })
                
                if should_log:
                    direction = "⬇️" if actual_adjustment < 0 else "⬆️" if actual_adjustment > 0 else "➖"
                    print(f"│ G{i+1:<4} │ {curr_pg:>8.2f}MW │ {new_pg:>8.2f}MW │ {direction} {abs(actual_adjustment):>6.2f}MW │")
            
            # 更新剩余不平衡量：剩余不平衡加上本轮调整的贡献
            # 如果增加了发电(正调整)，会减少"发电不足"(负不平衡)
            # 如果减少了发电(负调整)，会减少"发电过剩"(正不平衡)
            remaining_imbalance += total_adjustment
            
            # 更新当前时间段的最终不平衡
            self.current_segment_imbalance = remaining_imbalance
            
            if should_log:
                print(f"├─────────────────────────────────────────────────────────────")
                print(f"│ 本轮调整后剩余不平衡: {remaining_imbalance:.2f}MW")
                
            # 不尝试放宽爬坡限制，接受剩余的功率不平衡
        
        # 更新adjustment_results
        self.adjustment_results['adjustments'] = adjustment_history
        self.adjustment_results['final_imbalance'] = remaining_imbalance
        
        # 每个时间步的调整结束后，将当前时间步的最终不平衡量保存到residual_imbalance
        # 但不会影响下一个时间步的初始不平衡量，因为下一个时间步会重新计算
        self.residual_imbalance = remaining_imbalance
        self.current_segment_imbalance = remaining_imbalance
        
        if abs(remaining_imbalance) > BALANCE_THRESHOLD and should_log:
            print(f"│ ⚠️ 警告: {MAX_ITERATIONS}轮调整后仍有{remaining_imbalance:.2f}MW不平衡未被分配")
    
    def check_branch_limits(self):
        """
        检查线路功率流是否超过热极限
        """
        if self.branch_flows is None:
            raise ValueError("需要先运行潮流计算")
            
        # 使用线路数据中的RATE_A作为热极限
        thermal_limits = np.array([branch[5] for branch in self.branches])
            
        # 检查线路功率流是否超过热极限
        violations = {}
        total_violation = 0.0
        
        for i, flow in enumerate(self.branch_flows):
            limit = thermal_limits[i]
            if limit > 0:  # 只检查有限制的线路
                abs_flow = abs(flow)
                if abs_flow > limit:
                    f_bus = int(self.branches[i][0])
                    t_bus = int(self.branches[i][1])
                    violations[i] = {
                        'from_bus': f_bus,
                        'to_bus': t_bus,
                        'flow': flow,
                        'limit': limit,
                        'violation': abs_flow - limit,
                        'loading': abs_flow / limit * 100
                    }
                    total_violation += (abs_flow - limit)
                    
        # 添加汇总信息
        violations['summary'] = {
            'total_violations': len(violations) - 1,  # 减去summary自身
            'total_violation_mw': total_violation
        }
        
        return violations
    
    def get_power_balance(self):
        """
        获取功率平衡信息，直接使用计算的功率不平衡
        """
        # 计算总发电量
        total_gen = np.sum([gen[1] for gen in self.generators])
        
        # 计算总负荷
        total_load = np.sum(self.loads)

        # 总发电量（包含风电）
        total_gen_with_wind = total_gen + self.wind_power
        
        # 计算线路损耗（对于DC潮流应该是0）
        line_loss = 0.0
        
        # 使用计算的功率不平衡（如果有）
        if hasattr(self, 'residual_imbalance'):
            power_imbalance = self.residual_imbalance
        else:
            # 计算常规功率平衡（包含风电）
            power_imbalance = total_gen_with_wind - total_load - line_loss
            
        # 获取当前时间段不平衡
        segment_imbalance = 0.0
        if hasattr(self, 'current_segment_imbalance'):
            segment_imbalance = self.current_segment_imbalance
        
        return {
            'total_generation': total_gen_with_wind, # 返回总发电量
            'total_load': total_load,
            'line_loss': line_loss,
            'power_imbalance': power_imbalance,
            'segment_imbalance': segment_imbalance  # 添加时间段不平衡信息
        } 