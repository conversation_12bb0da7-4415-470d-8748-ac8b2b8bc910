import numpy as np
import random
import torch
import torch.nn as nn
import torch.optim as optim
import os, sys
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))
from utils.common import RunningMeanStd
from config import AGENT_CONFIG, DEVICE


class Actor(nn.Module):
    def __init__(self, state_dim, action_dim=80, action_bound=1.0, hidden_size=AGENT_CONFIG['hidden_size']):
        super(Actor, self).__init__()
        self.action_bound = action_bound
        self.net = nn.Sequential(
            nn.Linear(state_dim, hidden_size),
            nn.ReLU(),
            nn.Linear(hidden_size, hidden_size),
            nn.ReLU(),
            nn.Linear(hidden_size, hidden_size),
            nn.ReLU(),
            nn.Linear(hidden_size, hidden_size // 2),
            nn.ReLU(),
            nn.Linear(hidden_size // 2, action_dim),
            nn.Tanh()
        )

    def forward(self, x):
        return self.net(x)


class Critic(nn.Module):
    def __init__(self, state_dim, action_dim=80, hidden_size=AGENT_CONFIG['hidden_size']):
        super(Critic, self).__init__()
        self.net = nn.Sequential(
            nn.Linear(state_dim + action_dim, hidden_size),
            nn.ReLU(),
            nn.Linear(hidden_size, hidden_size // 2),
            nn.ReLU(),
            nn.Linear(hidden_size // 2, 1)
        )

    def forward(self, state, action):
        x = torch.cat([state, action], dim=1)
        return self.net(x).view(-1, 1)


class ReplayBuffer:
    def __init__(self, max_size=AGENT_CONFIG['buffer_size']):
        self.buffer = []
        self.max_size = max_size

    def push(self, transition):
        state, action, reward, next_state, done = transition
        if len(self.buffer) >= self.max_size:
            self.buffer.pop(0)
        self.buffer.append(transition)

    def sample(self, batch_size=AGENT_CONFIG['batch_size']):
        batch = random.sample(self.buffer, batch_size)
        states, actions, rewards, next_states, dones = zip(*batch)
        
        return (
            np.array(states),
            np.array(actions),
            np.array(rewards).reshape(-1, 1),  # 直接使用原始奖励
            np.array(next_states),
            np.array(dones).reshape(-1, 1)
        )

    def __len__(self):
        return len(self.buffer)


class DDPGAgent:
    """
    强化学习智能体: DDPG 实现
    改进：
    1. 添加reward归一化
    2. 改进网络更新机制
    3. 添加批归一化层
    """

    def __init__(
            self,
            state_dim,
            action_dim=80,
            action_bound=1.0,
            config=AGENT_CONFIG
    ):
        """
        初始化DDPG智能体
        """
        self.state_dim = state_dim
        self.action_dim = action_dim
        self.action_bound = action_bound
        self.buffer_size = config['buffer_size']
        self.batch_size = config['batch_size']
        self.gamma = config['gamma']
        self.tau = config['tau']

        # 初始化网络并移至GPU
        self.actor = Actor(state_dim, action_dim, action_bound).to(DEVICE)
        self.critic = Critic(state_dim, action_dim).to(DEVICE)
        self.target_actor = Actor(state_dim, action_dim, action_bound).to(DEVICE)
        self.target_critic = Critic(state_dim, action_dim).to(DEVICE)

        # 初始化优化器
        self.actor_optimizer = optim.Adam(self.actor.parameters(), lr=config['actor_lr'])
        self.critic_optimizer = optim.Adam(self.critic.parameters(), lr=config['critic_lr'], weight_decay=1e-2)

        # 初始化目标网络
        self._update_target(1.0)

        # 初始化经验回放缓冲区
        self.replay_buffer = ReplayBuffer(max_size=config['buffer_size'])

        # 动作探索用噪声
        self.noise_std = 0.1

    def select_action(self, state, noise=True, noise_std=None):
        """
        按当前策略选取动作，并添加随机噪声。
        动作为 80 维: [g_t(5维), g_{t+1}(5维), ..., g_{t+15}(5维)]。
        """
        state_tensor = torch.FloatTensor(state).unsqueeze(0).to(DEVICE)
        action_mean = self.actor(state_tensor).detach().cpu().numpy()[0]  # 范围在[-1,1]之间

        if noise:  # 只在训练时添加噪声
            std = noise_std if noise_std is not None else self.noise_std
            noise = np.random.normal(0, std, size=self.action_dim)
            action_noisy = action_mean + noise
            # 确保action在[-1,1]范围内
            action_noisy = np.clip(action_noisy, -1, 1)
        else:  # 评估时直接使用
            action_noisy = action_mean

        # 添加调试信息 - 仅打印前10个时间步的动作范围
        import os
        debug = os.environ.get('DEBUG_DDPG', '0') == '1'
        if debug:
            print("\n=== 动作信息 ===", flush=True)
            print(f"动作均值范围: [{np.min(action_mean):.4f}, {np.max(action_mean):.4f}]", flush=True)
            if noise:
                print(f"噪声标准差: {std:.4f}", flush=True)
                print(f"添加噪声后动作范围: [{np.min(action_noisy):.4f}, {np.max(action_noisy):.4f}]", flush=True)
            # 打印前两个时间步的动作
            for t in range(min(2, len(action_noisy) // 5)):
                time_step_actions = action_noisy[t*5:(t+1)*5]
                print(f"时间步{t}的动作: {time_step_actions}", flush=True)
            print("==================\n", flush=True)

        return action_noisy

    def update(self):
        """
        一次训练迭代
        """
        if len(self.replay_buffer) < self.batch_size:
            return 0.0, 0.0  # 返回actor和critic的损失

        # 采样并获取归一化后的reward
        states, actions, rewards, next_states, dones = self.replay_buffer.sample(self.batch_size)

        # 转换为tensor并移至GPU
        state_batch = torch.FloatTensor(states).to(DEVICE)
        action_batch = torch.FloatTensor(actions).to(DEVICE)
        reward_batch = torch.FloatTensor(rewards).to(DEVICE)  # 已经归一化
        next_state_batch = torch.FloatTensor(next_states).to(DEVICE)
        done_batch = torch.FloatTensor(dones).to(DEVICE)
        
        # 1) 更新Critic
        with torch.no_grad():
            next_actions = self.target_actor(next_state_batch)
            target_q = self.target_critic(next_state_batch, next_actions)
            # 移除目标Q值裁剪
            y = reward_batch + self.gamma * (1 - done_batch) * target_q

        current_q = self.critic(state_batch, action_batch)
        
        # 使用MSE损失计算当前Q值与目标Q值的差异
        critic_loss = nn.MSELoss()(current_q, y.detach())

        self.critic_optimizer.zero_grad()
        critic_loss.backward()
        # 移除梯度裁剪
        self.critic_optimizer.step()

        # 2) 更新Actor
        pred_actions = self.actor(state_batch)
        
        # 计算策略梯度损失
        actor_loss = -self.critic(state_batch, pred_actions).mean()

        self.actor_optimizer.zero_grad()
        actor_loss.backward()
        # 移除梯度裁剪
        self.actor_optimizer.step()

        # 3) 软更新目标网络
        self._update_target(self.tau)

        return actor_loss.item(), critic_loss.item()

    def _update_target(self, tau):
        """软更新目标网络"""
        for t_param, l_param in zip(self.target_actor.parameters(), self.actor.parameters()):
            t_param.data.copy_(tau * l_param.data + (1 - tau) * t_param.data)
        for t_param, l_param in zip(self.target_critic.parameters(), self.critic.parameters()):
            t_param.data.copy_(tau * l_param.data + (1 - tau) * t_param.data)

    def store_transition(self, state, action, reward, next_state, done, opf_action=None):
        """存储经验"""
        self.replay_buffer.push((state, action, reward, next_state, done))
