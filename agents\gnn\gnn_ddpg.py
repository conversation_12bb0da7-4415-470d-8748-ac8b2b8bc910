import sys
import os
import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from torch_geometric.nn import SAGEConv, global_mean_pool
from torch_geometric.data import Batch
import pandas as pd

# 添加项目根目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.abspath(os.path.join(current_dir, '..', '..'))
if project_root not in sys.path:
    sys.path.append(project_root)

from config import DEVICE
from config import AGENT_CONFIG, TRAIN_CONFIG
from utils.common import RunningMeanStd, RewardNormalizer
from agents.gnn.graph_utils import state_to_graph

class OptimalActionProvider:
    """提供最优动作数据的类"""
    
    def __init__(self, data_path, env):
        """
        初始化最优动作提供器
        
        Args:
            data_path: 最优发电机出力CSV文件路径
            env: 环境实例，用于获取时间点信息
        """
        self.optimal_data = pd.read_csv(data_path)
        self.env = env
        
        # 预处理数据，将发电机出力标准化到[-1,1]范围
        self._normalize_data()
        
        print(f"已加载{len(self.optimal_data)}条最优动作数据")
    
    def _normalize_data(self):
        """将受控发电机出力和风电数据归一化到[-1,1]范围"""
        # 1. 归一化受控发电机G2-G6 (排除平衡机G1)
        # 注意：controlled_gen_indices = [1, 2, 3, 4, 5] 对应 G2, G3, G4, G5, G6
        for gen_idx in self.env.controlled_gen_indices:
            col = f'G{gen_idx + 1}'  # gen_idx=1对应G2
            if col in self.optimal_data.columns:
                # 使用与环境相同的归一化方法
                gen_min = self.env.gen_min[gen_idx]
                gen_max = self.env.gen_max[gen_idx]

                # 归一化公式: 2*(x-min)/(max-min) - 1
                self.optimal_data[f'{col}_norm'] = (
                    2 * (self.optimal_data[col] - gen_min) / (gen_max - gen_min) - 1
                )

        # 2. 归一化风电数据
        if 'WindPower_MW' in self.optimal_data.columns and self.env.wind_power_enabled:
            wind_max = self.env.wind_power_max  # 50.0 MW
            # 风电归一化: 将[0, wind_max]映射到[-1, 1]
            self.optimal_data['Wind_norm'] = (
                2 * self.optimal_data['WindPower_MW'] / wind_max - 1
            )
    
    def get_optimal_action(self, time_point):
        """
        获取指定时间点的最优动作 - 修复版本

        Args:
            time_point: 时间点索引

        Returns:
            归一化的最优动作向量，如果没有找到则返回None
            格式: [G2_norm, G3_norm, G4_norm, G5_norm, G6_norm, Wind_norm]
        """
        # 查找对应时间点的数据
        time_col = 'TimePeriod' if 'TimePeriod' in self.optimal_data.columns else '时间点'
        data = self.optimal_data[self.optimal_data[time_col] == time_point]

        if len(data) == 0:
            return None

        # 提取归一化的动作: 按照adjusted_action的顺序
        optimal_action = []

        # 1. 受控发电机G2-G6 (按controlled_gen_indices顺序)
        for gen_idx in self.env.controlled_gen_indices:
            col = f'G{gen_idx + 1}_norm'  # gen_idx=1对应G2_norm
            if col in data.columns:
                optimal_action.append(data[col].values[0])
            else:
                print(f"警告: 未找到列 {col}")
                return None

        # 2. 风电数据
        if self.env.wind_power_enabled:
            if 'Wind_norm' in data.columns:
                optimal_action.append(data['Wind_norm'].values[0])
            else:
                print(f"警告: 未找到风电归一化数据")
                return None

        return torch.tensor(optimal_action, dtype=torch.float32, device=DEVICE)
    
    def get_batch_optimal_actions(self, system_states):
        """
        为一批状态获取最优动作 - 修复版本

        Args:
            system_states: 系统状态信息列表，每个元素是一个包含system_state字典的字典

        Returns:
            最优动作张量，形状与adjusted_action相同
            格式: [batch_size, 6] -> [G2, G3, G4, G5, G6, Wind]
        """
        batch_size = len(system_states)

        # 动态计算动作维度
        action_dim = len(self.env.controlled_gen_indices)
        if self.env.wind_power_enabled:
            action_dim += 1

        # 创建空的动作张量
        optimal_actions = torch.zeros((batch_size, action_dim), device=DEVICE)
        found_count = 0

        # 为每个样本获取最优动作
        for i in range(batch_size):
            if isinstance(system_states[i], dict) and 'system_state' in system_states[i]:
                if 'time_point' in system_states[i]['system_state']:
                    time_point = system_states[i]['system_state']['time_point']
                    action = self.get_optimal_action(time_point)

                    if action is not None and len(action) == action_dim:
                        optimal_actions[i] = action
                        found_count += 1

        if found_count == 0:
            print("警告: 未找到任何匹配的最优动作数据")
            return None
        elif found_count < batch_size:
            print(f"警告: 只找到 {found_count}/{batch_size} 个有效的最优动作")

        # 🔧 确保返回正确的批次维度
        if optimal_actions.dim() == 1:
            optimal_actions = optimal_actions.unsqueeze(0)  # [action_dim] -> [1, action_dim]

        return optimal_actions

# 添加残差块类
class ResidualBlock(nn.Module):
    def __init__(self, dim, dropout=0.1):
        super(ResidualBlock, self).__init__()
        # 重构层次顺序：Linear → LayerNorm → ReLU → Dropout
        self.fc1 = nn.Linear(dim, dim)
        self.ln1 = nn.LayerNorm(dim)
        self.relu = nn.ReLU()
        self.dropout = nn.Dropout(dropout)
        self.fc2 = nn.Linear(dim, dim)
        self.ln2 = nn.LayerNorm(dim)
    
    def forward(self, x):
        residual = x
        # 第一层
        out = self.fc1(x)
        out = self.ln1(out)  # 批归一化在激活函数之前
        out = self.relu(out)
        out = self.dropout(out)
        # 第二层
        out = self.fc2(out)
        out = self.ln2(out)  # 批归一化在激活函数之前
        # 残差连接
        out += residual
        return self.relu(out)

class GraphEncoder(nn.Module):
    """平衡的图编码器，2层GCN结构"""
    def __init__(self, node_feature_dim, hidden_dim, num_layers=2, dropout=0.3):
        super(GraphEncoder, self).__init__()
        self.num_layers = num_layers
        self.hidden_dim = hidden_dim

        # 输入投影层
        self.input_proj = nn.Linear(node_feature_dim, hidden_dim)

        # 恢复2层GCN，提供足够的特征提取能力
        self.conv_layers = nn.ModuleList()
        self.batch_norms = nn.ModuleList()
        for _ in range(num_layers):
            self.conv_layers.append(SAGEConv(hidden_dim, hidden_dim))
            self.batch_norms.append(nn.BatchNorm1d(hidden_dim))

        # 输出投影层
        self.output_proj = nn.Linear(hidden_dim, hidden_dim)

        # 适度的Dropout，平衡正则化与表达能力
        self.dropout = nn.Dropout(dropout)

        # 使用ReLU激活函数
        self.activation = nn.ReLU()

    def forward(self, data):
        # 输入投影
        x = self.input_proj(data.x)
        x = self.activation(x)
        x = self.dropout(x)

        # 2层GCN处理，轻量级残差连接
        identity = x
        for i in range(self.num_layers):
            x_conv = self.conv_layers[i](x, data.edge_index)
            x_conv = self.batch_norms[i](x_conv)
            x_conv = self.activation(x_conv)
            x_conv = self.dropout(x_conv)

            # 轻量级残差连接（仅在第二层）
            if i == 1:
                x = x_conv + identity
            else:
                x = x_conv

        # 输出投影
        x = self.output_proj(x)
        x = self.activation(x)

        return x


class GNNActor(nn.Module):
    """简化的Actor网络，移除残差块，使用简单MLP结构"""
    def __init__(self, node_feature_dim, edge_feature_dim, hidden_dim, action_dim, time_steps=4):
        super(GNNActor, self).__init__()
        self.node_feature_dim = node_feature_dim
        self.time_steps = time_steps
        self.action_dim = action_dim

        # 使用简化的单层GraphEncoder
        self.encoder = GraphEncoder(
            node_feature_dim=node_feature_dim,
            hidden_dim=hidden_dim,
            num_layers=1,
            dropout=0.3
        )

        # 添加轻量级残差块的MLP结构
        self.fc1 = nn.Linear(hidden_dim, hidden_dim)
        self.ln1 = nn.LayerNorm(hidden_dim)
        self.dropout1 = nn.Dropout(0.3)

        # 单个残差块
        self.residual_block = ResidualBlock(hidden_dim, dropout=0.2)

        self.fc2 = nn.Linear(hidden_dim, hidden_dim // 2)
        self.ln2 = nn.LayerNorm(hidden_dim // 2)
        self.dropout2 = nn.Dropout(0.2)

        self.fc_out = nn.Linear(hidden_dim // 2, action_dim)

        self.activation = nn.ReLU()
        self.tanh = nn.Tanh()

        # 使用更稳定的初始化方法
        self.apply(self._init_weights)
        
    def _init_weights(self, module):
        if isinstance(module, nn.Linear):
            # 使用Kaiming初始化，适用于ReLU激活函数
            nn.init.kaiming_normal_(module.weight.data, a=0.01, mode='fan_out')
            
            # 对于最后一层使用特殊初始化
            if module.out_features == self.action_dim:
                # 使用较小的标准差初始化输出层，使初始动作接近零
                nn.init.normal_(module.weight.data, mean=0.0, std=0.003)
                
            if module.bias is not None:
                if module.out_features == self.action_dim:
                    # 输出层的偏置初始化为零
                    module.bias.data.zero_()
                else:
                    # 隐藏层的偏置使用小的正值初始化，避免死亡ReLU
                    nn.init.constant_(module.bias.data, 0.01)

    def forward(self, graph_list):
        if isinstance(graph_list, list) and len(graph_list) == 1:
            # 单个样本处理
            batch = Batch.from_data_list(graph_list)
            batch.batch = torch.zeros(batch.x.size(0), dtype=torch.long, device=DEVICE)

            # 获取节点嵌入
            node_embeddings = self.encoder(batch)

            # 全局池化
            graph_embeddings = global_mean_pool(node_embeddings, batch.batch)

            # 通过改进的MLP结构
            x = self.fc1(graph_embeddings)
            x = self.ln1(x)
            x = self.activation(x)
            x = self.dropout1(x)

            # 通过残差块
            x = self.residual_block(x)

            # 输出层
            x = self.fc2(x)
            x = self.ln2(x)
            x = self.activation(x)
            x = self.dropout2(x)

            actions = self.fc_out(x)
            actions = self.tanh(actions)
            return actions.squeeze(0)
        else:
            # 批处理
            batch = Batch.from_data_list(graph_list)
            batch_size = len(graph_list) // self.time_steps
            batch.batch = torch.arange(batch_size * self.time_steps, device=DEVICE).repeat_interleave(graph_list[0].x.size(0))

            # 获取节点嵌入
            node_embeddings = self.encoder(batch)

            # 全局池化
            graph_embeddings = global_mean_pool(node_embeddings, batch.batch)
            graph_embeddings = graph_embeddings.view(batch_size, self.time_steps, -1)
            graph_embeddings = torch.mean(graph_embeddings, dim=1)  # 平均池化时间维度

            # 通过改进的MLP结构
            x = self.fc1(graph_embeddings)
            x = self.ln1(x)
            x = self.activation(x)
            x = self.dropout1(x)

            # 通过残差块
            x = self.residual_block(x)

            # 输出层
            x = self.fc2(x)
            x = self.ln2(x)
            x = self.activation(x)
            x = self.dropout2(x)

            actions = self.fc_out(x)
            actions = self.tanh(actions)
            return actions


class GNNCritic(nn.Module):
    """简化的Critic网络，移除残差块，使用简单MLP结构"""
    def __init__(self, node_feature_dim, edge_feature_dim, hidden_dim, action_dim, time_steps=4):
        super(GNNCritic, self).__init__()
        self.node_feature_dim = node_feature_dim
        self.time_steps = time_steps

        # 使用简化的单层GraphEncoder
        self.encoder = GraphEncoder(
            node_feature_dim=node_feature_dim,
            hidden_dim=hidden_dim,
            num_layers=1,
            dropout=0.4  # 增加dropout以防止过拟合
        )

        # 动作编码器
        action_hidden_dim = hidden_dim // 2
        self.action_encoder = nn.Sequential(
            nn.Linear(action_dim, action_hidden_dim),
            nn.LayerNorm(action_hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.3)
        )

        # 简化的MLP结构，移除残差块
        self.mlp = nn.Sequential(
            nn.Linear(hidden_dim + action_hidden_dim, hidden_dim),
            nn.LayerNorm(hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.4),

            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.LayerNorm(hidden_dim // 2),
            nn.ReLU(),
            nn.Dropout(0.3),

            nn.Linear(hidden_dim // 2, 1)  # Q值输出
        )

        # 使用更稳定的初始化方法
        self.apply(self._init_weights)
        
    def _init_weights(self, module):
        if isinstance(module, nn.Linear):
            # 使用Kaiming初始化，适用于ReLU激活函数
            nn.init.kaiming_normal_(module.weight.data, a=0.01, mode='fan_out')
            
            # 对于最后一层使用特殊初始化
            if module.out_features == 1:  # 输出层
                # Q值初始化为接近零的小负值
                nn.init.normal_(module.weight.data, mean=0.0, std=0.003)
                
            if module.bias is not None:
                if module.out_features == 1:
                    # 输出层的偏置初始化为零
                    module.bias.data.zero_()
                else:
                    # 隐藏层的偏置使用小的正值初始化，避免死亡ReLU
                    nn.init.constant_(module.bias.data, 0.01)
                
    def forward(self, graph_list, action):
        # 处理图输入
        batch = Batch.from_data_list(graph_list)
        batch_size = len(graph_list) // self.time_steps
        batch.batch = torch.arange(batch_size * self.time_steps, device=DEVICE).repeat_interleave(graph_list[0].x.size(0))

        # 获取节点嵌入
        node_embeddings = self.encoder(batch)

        # 全局池化
        graph_embeddings = global_mean_pool(node_embeddings, batch.batch)
        graph_embeddings = graph_embeddings.view(batch_size, self.time_steps, -1)
        graph_embeddings = torch.mean(graph_embeddings, dim=1)  # 平均池化时间维度

        # 编码动作 - 确保action有正确的批次维度
        if action.dim() == 1:
            # 如果action是1维的，添加批次维度
            action = action.unsqueeze(0)

        # 确保action的批次维度与graph_embeddings匹配
        if action.size(0) != graph_embeddings.size(0):
            # 如果批次大小不匹配，重复action以匹配批次大小
            action = action.repeat(graph_embeddings.size(0), 1)

        action_embeddings = self.action_encoder(action)

        # 连接图嵌入和动作嵌入
        combined = torch.cat([graph_embeddings, action_embeddings], dim=1)

        # 通过简化的MLP
        q_value = self.mlp(combined)

        # 轻度压缩Q值幅度，保持足够的梯度信号
        q_value = 0.05 * q_value  # 减少缩放，增强梯度信号

        return q_value


class EpisodePrioritizedReplayBuffer:
    """基于Episode的优先经验回放缓冲区"""
    def __init__(self, max_size, alpha=0.6, beta=0.4, beta_increment=0.001, epsilon=1e-6):
        self.buffer = []  # 存储完整episode的经验
        self.priorities = []  # 存储episode级别的优先级
        self.max_size = max_size  # 最大存储的episode数
        self.alpha = alpha  # 优先级指数
        self.beta = beta    # 重要性采样指数
        self.beta_increment = beta_increment  # beta逐步增加到1
        self.epsilon = epsilon  # 防止优先级为0
        self.max_priority = 1.0  # 最大优先级
        
        # 当前episode的临时存储
        self.current_episode = []
        
        # 总经验计数
        self.experience_count = 0
    
    def start_episode(self):
        """开始一个新的episode"""
        self.current_episode = []
    
    def add_to_current_episode(self, transition):
        """向当前episode添加一个transition"""
        self.current_episode.append(transition)
    
    def end_episode_and_add(self):
        """结束当前episode并将其添加到缓冲区"""
        if not self.current_episode:
            return  # 如果当前episode为空，不做处理
            
        # 使用最大优先级，不再基于cost_ratio调整
        priority = self.max_priority
            
        # 为episode中的每个transition添加必要信息
        episode_with_info = []
        for transition in self.current_episode:
            transition_copy = transition.copy()
            episode_with_info.append(transition_copy)
            
        # 将episode添加到缓冲区
        if len(self.buffer) >= self.max_size:
            # 移除最旧的episode
            removed_episode = self.buffer.pop(0)
            self.priorities.pop(0)
            self.experience_count -= len(removed_episode)
            
        self.buffer.append(episode_with_info)
        self.priorities.append(priority)
        self.experience_count += len(episode_with_info)
            
        # 清空当前episode
        self.current_episode = []
    
    def sample(self, batch_size):
        """基于episode优先级采样一批transitions"""
        if not self.buffer:
            return {}
            
        # 更新beta值，逐渐接近1
        self.beta = min(1.0, self.beta + self.beta_increment)
        
        # 计算episode的采样概率
        priorities = np.array(self.priorities)
        probabilities = priorities ** self.alpha
        probabilities /= probabilities.sum()
        
        # 采样episodes
        episode_indices = np.random.choice(len(self.buffer), min(len(self.buffer), batch_size), 
                                          p=probabilities, replace=True)
        
        # 计算重要性采样权重 (episode级别)
        weights = (len(self.buffer) * probabilities[episode_indices]) ** (-self.beta)
        weights /= weights.max()  # 归一化权重
        
        # 从选择的episodes中采样transitions
        transitions = []
        transition_weights = []
        sampled_indices = []  # (episode_idx, transition_idx)
        
        for i, episode_idx in enumerate(episode_indices):
            episode = self.buffer[episode_idx]
            if not episode:
                continue
                
            # 随机选择一个transition
            transition_idx = np.random.randint(0, len(episode))
            transition = episode[transition_idx]
            
            transitions.append(transition)
            transition_weights.append(weights[i])
            sampled_indices.append((episode_idx, transition_idx))
            
        if not transitions:
            return {}
            
        # 准备batch数据
        state_graphs_batch = []
        next_state_graphs_batch = []
        actions_batch = []
        rewards_batch = []
        original_rewards_batch = []
        dones_batch = []
        reward_components_batch = []
        system_states_batch = []
        
        for transition in transitions:
            # 基本数据
            state_graphs_batch.extend(transition['state_graphs'])
            if transition['next_state_graphs'] is not None:
                next_state_graphs_batch.extend(transition['next_state_graphs'])
            else:
                next_state_graphs_batch.extend(transition['state_graphs'])
            actions_batch.append(transition['action'])
            rewards_batch.append(transition['reward'])
            
            if 'original_reward' in transition:
                original_rewards_batch.append(transition['original_reward'])
                
            dones_batch.append(transition['done'])
            
            # 额外信息
            if 'reward_components' in transition:
                reward_components_batch.append(transition['reward_components'])
            if 'system_state' in transition:
                system_states_batch.append({'system_state': transition['system_state']})
        
        # 堆叠tensor
        actions_batch = torch.stack(actions_batch)
        rewards_batch = torch.stack(rewards_batch).view(-1, 1)
        dones_batch = torch.stack(dones_batch).view(-1, 1)
        weights_tensor = torch.tensor(transition_weights, dtype=torch.float32, device=DEVICE).view(-1, 1)
        
        # 创建返回字典
        batch_dict = {
            'state_graphs': state_graphs_batch,
            'next_state_graphs': next_state_graphs_batch,
            'actions': actions_batch,
            'reward': rewards_batch,
            'dones': dones_batch,
            'weights': weights_tensor,
            'indices': sampled_indices
        }
        
        # 添加原始奖励（如果有）
        if original_rewards_batch:
            batch_dict['original_reward'] = torch.stack(original_rewards_batch).view(-1, 1)
        
        # 添加额外信息（如果有）
        if reward_components_batch:
            batch_dict['reward_components'] = reward_components_batch
        if system_states_batch:
            batch_dict['system_states'] = system_states_batch
        
        return batch_dict
    
    def update_priorities(self, indices, td_errors):
        """更新episode的优先级"""
        for i, (episode_idx, _) in enumerate(indices):
            if episode_idx < len(self.priorities):
                # 基础优先级 = TD误差 + 小常数
                priority = abs(td_errors[i]) + self.epsilon
                
                # 更新最大优先级（用于新episode）
                self.max_priority = max(self.max_priority, priority)
                
                # 更新索引对应的优先级
                self.priorities[episode_idx] = priority
    
    def __len__(self):
        """返回总经验数量"""
        return self.experience_count


class OUNoise:
    """改进的Ornstein-Uhlenbeck过程噪声生成器，增强探索能力"""
    def __init__(self, action_dimension, mu=0, theta=0.05, sigma=0.15):  # 调整sigma为0.15，减小噪声幅度
        self.action_dimension = action_dimension
        self.mu = mu
        self.theta = theta  # 降低theta使噪声更持久，从0.1降到0.05
        self.sigma = sigma  # 提高sigma增大噪声振幅，从0.15提高到0.25
        self.state = torch.ones(self.action_dimension, device=DEVICE) * self.mu
        self.reset()

    def reset(self):
        self.state = torch.ones(self.action_dimension, device=DEVICE) * self.mu

    def sample(self, progress=0.0):
        """
        采样噪声，根据训练进度平滑调整噪声强度
        Args:
            progress: 训练进度 (0~1)
        """
        x = self.state
        # 使用改进的余弦退火调度，更缓慢地减小噪声
        decay = 0.5 * (1.0 + np.cos(np.pi * min(progress * 0.8, 1.0)))  # 延长探索期
        current_sigma = self.sigma * decay
        
        # 生成噪声
        dx = self.theta * (self.mu - x) + current_sigma * torch.randn(len(x), device=DEVICE)
        self.state = x + dx
        return self.state


class GNNDDPGAgent:
    """基于图神经网络的DDPG智能体"""
    def __init__(
            self,
            node_features,
            edge_features,
            action_dim=20,
            action_bound=1.0,
            time_steps=4,
            hidden_dim=256,  # 使用较大的隐藏层维度以增强网络容量
            config=AGENT_CONFIG
    ):
        """
        初始化GNNDDPG代理
        
        Args:
            node_features: 节点特征维度
            edge_features: 边特征维度
            action_dim: 动作维度
            action_bound: 动作边界
            time_steps: 时间步数
            hidden_dim: 隐藏层维度
            config: 配置字典
        """
        # 基本参数
        self.node_features = node_features
        self.edge_features = edge_features
        self.action_dim = action_dim
        self.action_bound = action_bound
        self.time_steps = time_steps
        self.hidden_dim = hidden_dim
        self.device = DEVICE
        
        # === 直接从配置文件读取，不设置默认值 ===
        # 学习率配置
        self.actor_lr = config['actor_lr']
        self.critic_lr = config['critic_lr']
        
        # 强化学习基础参数
        self.gamma = config['gamma']
        self.tau = config['tau']
        self.buffer_size = config['buffer_size']
        self.batch_size = config['batch_size']
        
        # 探索策略参数
        self.initial_noise_scale = config['initial_noise_scale']
        self.final_noise_scale = config['final_noise_scale']
        self.exploration_steps = config['exploration_steps']
        
        # 监督学习参数 - 三段式策略配置
        self.initial_supervision_weight = config['initial_supervision_weight']
        self.plateau_supervision_weight = config['plateau_supervision_weight']
        self.final_supervision_weight = config['final_supervision_weight']

        # 三段式策略比例
        self.supervision_stage1_ratio = config['supervision_stage1_ratio']
        self.supervision_stage2_ratio = config['supervision_stage2_ratio']
        self.supervision_stage3_ratio = config['supervision_stage3_ratio']
        
        # 损失权重配置
        self.actor_loss_weight = config['actor_loss_weight']
        self.critic_loss_weight = config['critic_loss_weight']
        self.grad_clip_norm = config.get('grad_clip_norm', 1.0)
        self.noise_scale = self.initial_noise_scale
        self.noise = OUNoise(action_dim)

        # 网络更新频率配置
        self.update_frequency = config.get('update_frequency', 1)
        self.updates_per_step = config.get('updates_per_step', 1)
        self.actor_update_ratio = config.get('actor_update_ratio', 2)  # 默认每2步更新一次Actor
        
        # 保存环境的graph_builder对象和环境引用
        self.env_graph_builder = None
        self.env = None  # 保存环境引用，用于动作归一化
        
        # 初始化简化的网络并移至GPU
        self.actor = GNNActor(node_features, edge_features, hidden_dim, action_dim, time_steps).to(DEVICE)
        self.critic = GNNCritic(node_features, edge_features, hidden_dim, action_dim, time_steps).to(DEVICE)

        # 目标网络
        self.target_actor = GNNActor(node_features, edge_features, hidden_dim, action_dim, time_steps).to(DEVICE)
        self.target_critic = GNNCritic(node_features, edge_features, hidden_dim, action_dim, time_steps).to(DEVICE)
        
        # 初始化优化器
        self.actor_optimizer = optim.Adam(self.actor.parameters(), lr=self.actor_lr, weight_decay=1e-4)  # 添加权重衰减
        self.critic_optimizer = optim.Adam(self.critic.parameters(), lr=self.critic_lr, weight_decay=1e-3)  # 增加weight_decay提供更强的正则化
        
        # 初始化目标网络
        self._update_target(1.0)
        
        # 初始化经验回放缓冲区
        self.replay_buffer = EpisodePrioritizedReplayBuffer(max_size=self.buffer_size)
        
        # 添加学习率调整和损失权重
        self.policy_reg_weight = 0.005  # 减小策略正则化权重
        self.episodes_trained = 0
        self.best_reward = -float('inf')
        self.best_reward_episode = 0
        
        # 添加学习率调度器
        self.actor_scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(
            self.actor_optimizer, mode='max', factor=0.5, patience=50)
        self.critic_scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(
            self.critic_optimizer, mode='max', factor=0.5, patience=50)
        
        # 添加奖励归一化器
        self.reward_normalizer = RewardNormalizer(device=DEVICE)
        
        # 探索相关参数
        self.min_reward = float('inf')     # 用于追踪最小奖励
        self.max_reward = float('-inf')    # 用于追踪最大奖励
        self.reward_history = []           # 用于存储最近的奖励

        # === policy delay 计数器 ===
        self.update_step = 0  # 用于控制Actor延迟更新
        
        # 添加监督学习相关参数 - 【改进2】优化监督学习策略
        self.optimal_action_provider = None  # 最优动作提供器
        self.use_supervision = False  # 是否使用监督学习
        
        # 监督学习损失历史
        self.supervision_loss_history = []
        self.policy_loss_history = []
        self.supervision_weight_history = []
        
        # 添加当前回合的监督损失和策略损失记录变量
        self.current_episode_supervision_loss = 0.0
        self.current_episode_policy_loss = 0.0
        self.current_episode_update_count = 0

        # === 新增：关键指标监控历史记录 ===
        self.q_value_history = []           # Q值统计历史
        self.target_q_history = []          # 目标Q值统计历史
        self.td_error_history = []          # TD误差统计历史
        self.actor_grad_norm_history = []   # Actor梯度范数历史
        self.critic_grad_norm_history = []  # Critic梯度范数历史
        self.action_std_history = []        # 动作标准差历史（探索程度）
    
    def set_optimal_action_provider(self, optimal_action_provider):
        """设置最优动作提供器"""
        self.optimal_action_provider = optimal_action_provider
        self.use_supervision = True
        print("已设置最优动作提供器，启用监督学习")

    def set_env(self, env):
        """设置环境引用，用于动作归一化"""
        self.env = env
    
    def get_supervision_weight(self):
        """
        计算当前的监督学习权重，实现灵活的三段式衰减

        三段式策略（基于总训练回合数的百分比）：
        - 第一阶段 (前30%)：从initial_weight线性衰减到0.3
        - 第二阶段 (中间40%)：保持在0.3不变
        - 第三阶段 (后30%)：从0.3线性衰减到final_weight

        Returns:
            当前回合的监督权重
        """
        if not self.use_supervision:
            return 0.0
            
        total_episodes = TRAIN_CONFIG.get('num_episodes', 800)
        current_episode = self.episodes_trained
        
        # 灵活的三段式监督权重衰减策略（从配置文件读取参数）
        stage1_ratio = self.supervision_stage1_ratio  # 第一阶段比例
        stage2_ratio = self.supervision_stage2_ratio  # 第二阶段比例
        stage3_ratio = self.supervision_stage3_ratio  # 第三阶段比例

        stage1_end = int(total_episodes * stage1_ratio)  # 第一阶段结束
        stage2_end = int(total_episodes * (stage1_ratio + stage2_ratio))  # 第二阶段结束

        initial_weight = self.initial_supervision_weight    # 初始权重
        plateau_weight = self.plateau_supervision_weight    # 平台期权重
        final_weight = self.final_supervision_weight        # 最终权重

        if current_episode < stage1_end:
            # 第一阶段：从initial_weight线性衰减到plateau_weight
            if stage1_end > 0:
                progress = current_episode / stage1_end
                weight = initial_weight - progress * (initial_weight - plateau_weight)
            else:
                weight = plateau_weight
        elif current_episode < stage2_end:
            # 第二阶段：保持在plateau_weight
            weight = plateau_weight
        else:
            # 第三阶段：从plateau_weight线性衰减到final_weight
            remaining_episodes = total_episodes - stage2_end
            if remaining_episodes > 0:
                progress = (current_episode - stage2_end) / remaining_episodes
                progress = min(progress, 1.0)  # 确保progress不超过1
                weight = plateau_weight - progress * (plateau_weight - final_weight)
            else:
                weight = final_weight
            
        return max(weight, final_weight) # 确保权重不低于最终值
    
    def _adjust_noise_scale(self, episode_reward):
        """根据训练表现动态调整噪声强度"""
        # 仅按训练进度线性衰减噪声，不再基于奖励自适应
        progress = min(self.episodes_trained / self.exploration_steps, 1.0)
        self.noise_scale = self.initial_noise_scale * (1.0 - progress) + self.final_noise_scale * progress

    def select_action(self, state, graph_builder, add_noise=True):
        """
        选择动作（始终保留梯度用于监督学习）

        Args:
            state: 状态向量
            graph_builder: IEEE30GraphBuilder实例
            add_noise: 是否添加噪声

        Returns:
            action: 动作向量 [action_dim]（在GPU上的tensor，保留梯度）
        """
        # 如果当前没有保存环境的graph_builder，则保存传入的graph_builder
        if self.env_graph_builder is None:
            self.env_graph_builder = graph_builder
        
        # 优先使用环境的graph_builder对象确保一致性
        builder_to_use = self.env_graph_builder or graph_builder
        
        # 计算期望的node_feature_dim值
        expected_node_feature_dim = state.shape[0] // self.time_steps
        
        # 将状态向量转换为图数据列表，总是使用根据状态向量计算的节点特征维度
        graph_list = state_to_graph(
            state, 
            builder_to_use, 
            time_steps=self.time_steps, 
            node_feature_dim=expected_node_feature_dim  # 使用计算得到的值而不是self.node_features
        )
        
        # 始终保留梯度，保持训练模式
        self.actor.train()
        action_mean = self.actor(graph_list)

        if action_mean.dim() > 1:
            action_mean = action_mean.mean(dim=0)

        if add_noise:
            # 【改进1.2】改进噪声混合策略
            # 计算基础噪声
            gaussian_noise = torch.randn_like(action_mean) * self.noise_scale
            # 获取OU噪声
            progress = min(self.episodes_trained / self.exploration_steps, 1.0)
            ou_noise = self.noise.sample(progress) * self.noise_scale

            # 【改进1.3】更平缓的噪声混合策略
            noise_ratio = np.cos(progress * np.pi/2.5)
            combined_noise = noise_ratio * gaussian_noise + (1 - noise_ratio) * ou_noise

            # 添加噪声并裁剪
            action = action_mean + combined_noise
            action = torch.clamp(action, -1, 1)
        else:
            action = action_mean
        
        # 确保返回的是一维向量
        if action.dim() > 1:
            action = action.squeeze()

        # 始终保留梯度
        return action

    def _normalize_action_for_storage(self, action, info=None):
        """
        将adjusted_action(MW值)归一化到[-1,1]范围用于存储

        Args:
            action: adjusted_action张量，包含实际发电机出力(MW)和风电出力(MW)
            info: 包含环境信息的字典

        Returns:
            归一化后的动作张量 [-1, 1]
        """
        # 获取环境信息，优先使用直接保存的环境引用
        env = self.env
        if env is None and hasattr(self, 'optimal_action_provider') and self.optimal_action_provider is not None:
            env = self.optimal_action_provider.env

        if env is None:
            # 如果无法获取环境信息，返回原始动作（向后兼容）
            print("警告: 无法获取环境信息进行动作归一化，使用原始动作")
            return action

        normalized_action = torch.zeros_like(action)

        # 归一化受控发电机出力
        for i, gen_idx in enumerate(env.controlled_gen_indices):
            if i < len(action):
                gen_min = env.gen_min[gen_idx]
                gen_max = env.gen_max[gen_idx]
                # 归一化公式: 2*(x-min)/(max-min) - 1
                normalized_action[i] = (2 * (action[i] - gen_min) / (gen_max - gen_min) - 1)

        # 归一化风电出力
        if env.wind_power_enabled and len(action) > len(env.controlled_gen_indices):
            wind_idx = len(env.controlled_gen_indices)
            wind_max = env.wind_power_max
            # 风电归一化: 将[0, wind_max]映射到[-1, 1]
            normalized_action[wind_idx] = (2 * action[wind_idx] / wind_max - 1)

        return normalized_action

    def _differentiable_adjust_batch_actions(self, raw_actions, batch_info):
        """
        对批量动作进行可微分调节

        Args:
            raw_actions: Actor原始输出 [batch_size, action_dim]
            batch_info: 批次信息，包含系统状态等

        Returns:
            adjusted_actions: 调节后的动作，保留梯度
        """
        if self.env is None:
            print("警告: 环境引用为空，无法进行可微分调节，返回原始动作")
            return raw_actions

        batch_size = raw_actions.shape[0]
        # 🔧 修复：使用列表收集结果，避免inplace操作
        adjusted_actions_list = []

        # 对批次中的每个样本进行调节
        for i in range(batch_size):
            try:
                # 获取单个样本的动作
                single_action = raw_actions[i]

                # 尝试从batch_info获取系统状态信息
                prev_gen_output = None
                load_demand = None

                if 'system_states' in batch_info and batch_info['system_states']:
                    system_state = batch_info['system_states'][i]
                    if isinstance(system_state, dict):
                        prev_gen_output = system_state.get('prev_gen_output')
                        load_demand = system_state.get('load_demand')

                # 调用环境的可微分调节函数
                adjusted_action = self.env.differentiable_adjust_action(
                    single_action, prev_gen_output, load_demand
                )

                # 🔧 修复：添加到列表而不是inplace赋值
                adjusted_actions_list.append(adjusted_action)

            except Exception as e:
                print(f"警告: 第{i}个样本调节失败: {e}，使用原始动作")
                # 🔧 修复：添加到列表而不是inplace赋值
                adjusted_actions_list.append(raw_actions[i])

        # 🔧 修复：从列表创建新tensor，避免inplace操作
        adjusted_actions = torch.stack(adjusted_actions_list)

        return adjusted_actions

    def _safe_differentiable_adjust_batch_actions(self, raw_actions, batch_info):
        """
        安全的批量可微分调节函数，避免inplace操作

        Args:
            raw_actions: Actor原始输出 [batch_size, action_dim]
            batch_info: 批次信息，包含系统状态等

        Returns:
            adjusted_actions: 调节后的动作，保留梯度
        """
        if self.env is None:
            print("警告: 环境引用为空，无法进行可微分调节，返回原始动作")
            return raw_actions

        batch_size = raw_actions.shape[0]
        adjusted_actions_list = []

        # 对批次中的每个样本进行调节
        for i in range(batch_size):
            # 获取单个样本的动作，创建新的tensor避免inplace操作
            single_action = raw_actions[i].clone()

            # 尝试从batch_info获取系统状态信息
            prev_gen_output = None
            load_demand = None

            if 'system_states' in batch_info and batch_info['system_states']:
                system_state = batch_info['system_states'][i]
                if isinstance(system_state, dict):
                    prev_gen_output = system_state.get('prev_gen_output')
                    load_demand = system_state.get('load_demand')

            # 调用环境的可微分调节函数
            adjusted_action = self.env.differentiable_adjust_action(
                single_action, prev_gen_output, load_demand
            )

            adjusted_actions_list.append(adjusted_action)

        # 从列表创建新tensor，完全避免inplace操作
        adjusted_actions = torch.stack(adjusted_actions_list)

        return adjusted_actions

    def store_transition(self, state_graphs, action, reward, next_state_graphs, done, info=None):
        """
        存储一个转换(state_graphs, action, reward, next_state_graphs, done)到当前episode
        state_graphs和next_state_graphs是图的列表
        
        Args:
            state_graphs: 当前状态的图列表
            action: 动作tensor
            reward: 奖励值（负值，表示成本和惩罚）
            next_state_graphs: 下一状态的图列表
            done: 是否结束
            info: 包含额外信息的字典，如奖励分解、系统状态等
            
        Returns:
            reward: 原始奖励值
        """
        # 🔧 安全策略：存储时断开梯度，训练时重新生成梯度
        if not isinstance(action, torch.Tensor):
            action = torch.tensor(action, dtype=torch.float32, device=DEVICE)
        else:
            # 存储时断开梯度连接，避免经验回放时的梯度冲突
            action = action.detach().clone()

        if not isinstance(reward, torch.Tensor):
            reward = torch.tensor(reward, dtype=torch.float32, device=DEVICE)

        if not isinstance(done, torch.Tensor):
            done = torch.tensor(done, dtype=torch.float32, device=DEVICE)

        # 归一化动作：将adjusted_action(MW值)归一化到[-1,1]范围
        normalized_action = self._normalize_action_for_storage(action, info)
        
        # 提取奖励组件（如果有）
        reward_components = None
        if info and 'reward_components' in info:
            reward_components = {}
            for key, value in info['reward_components'].items():
                # 跳过已取消的功率平衡惩罚
                if key in ['power_balance']:
                    continue
                    
                # 确保张量在正确设备上
                if not isinstance(value, torch.Tensor):
                    reward_components[key] = torch.tensor(value, dtype=torch.float32, device=DEVICE)
                else:
                    reward_components[key] = value
                    
            # 确保gen_limit存在于组件中
            if 'gen_limit' not in reward_components and 'gen_limit' in info['reward_components']:
                value = info['reward_components']['gen_limit']
                if not isinstance(value, torch.Tensor):
                    reward_components['gen_limit'] = torch.tensor(value, dtype=torch.float32, device=DEVICE)
                else:
                    reward_components['gen_limit'] = value
        
        # 使用归一化器归一化奖励
        # ===== 新增: 根据每个时间步的最优发电成本动态更新基准值 =====
        if info is not None and 'optimal_cost' in info and info['optimal_cost'] is not None:
            try:
                optimal_cost_value = float(info['optimal_cost'])
                if optimal_cost_value > 0:  # 仅在有效数值时更新
                    self.reward_normalizer.update_base_values({'gen_cost': optimal_cost_value})
            except (TypeError, ValueError):
                # 若转换失败则忽略，不中断训练流程
                pass
        # ============================================================
        normalized_reward = reward
        original_reward = reward.clone()
        if reward_components:
            normalized_reward = self.reward_normalizer.normalize(reward, reward_components)
        
        # 创建包含完整信息的transition
        transition = {
            'state_graphs': state_graphs,
            'action': normalized_action,     # 使用归一化动作存储
            'original_action': action,       # 保存原始动作(MW值)
            'reward': normalized_reward,     # 使用归一化奖励存储
            'original_reward': original_reward,  # 保存原始奖励
            'next_state_graphs': next_state_graphs,
            'done': done
        }
        
        # 添加额外信息
        if reward_components:
            transition['reward_components'] = reward_components
            
        if info and 'system_state' in info:
            transition['system_state'] = info['system_state']
            
        # 将转换添加到当前episode
        self.replay_buffer.add_to_current_episode(transition)
        
        # 返回原始奖励和归一化奖励
        return original_reward, normalized_reward
        
    def end_episode(self):
        """
        结束当前episode，并将其添加到回放缓冲区
        """
        # 将当前episode添加到回放缓冲区
        self.replay_buffer.end_episode_and_add()
        
        # 开始新的episode
        self.replay_buffer.start_episode()
    
    def start_episode(self):
        """开始新的episode"""
        self.replay_buffer.start_episode()
    
    def update(self):
        """
        更新策略和价值网络，优化版本
        
        Returns:
            actor_loss: Actor网络的损失
            critic_loss: Critic网络的损失
        """
        # 如果缓冲区大小不足，则不更新
        min_buffer_size = min(self.batch_size, 32)
        if len(self.replay_buffer) < min_buffer_size:
            # 添加调试信息，说明为什么不更新
            if self.episodes_trained % 10 == 0:  # 每10个回合打印一次
                print(f"缓冲区大小不足: {len(self.replay_buffer)}/{min_buffer_size}, 跳过更新")
            return 0.0, 0.0
        
        # === 1) 更新Critic：使用更稳定的策略 ===
        # 记录调用次数，实现policy delay
        self.update_step += 1

        critic_loss_total = 0
        critic_update_times = 1  # 减少到1次，避免过度拟合

        # 初始化监控指标变量
        q_mean = q_std = q_min = q_max = 0.0
        target_q_mean = target_q_std = target_q_min = target_q_max = 0.0
        td_mean = td_std = td_max = 0.0
        critic_grad_norm = 0.0
        actor_grad_norm = 0.0
        action_std = 0.0
        
        for _ in range(critic_update_times):
            # 重新采样批次，增加样本多样性
            critic_batch = self.replay_buffer.sample(self.batch_size)
            
            # 获取重要性采样权重和索引（用于更新优先级）
            weights = critic_batch.get('weights', None)
            indices = critic_batch.get('indices', None)
            
            with torch.no_grad():
                next_actions = self.target_actor(critic_batch['next_state_graphs'])
                # 减少目标动作噪声，提高稳定性
                noise = torch.randn_like(next_actions) * 0.02  # 进一步降低到0.02
                next_actions = torch.clamp(next_actions + noise, -1, 1)
                
                target_q = self.target_critic(critic_batch['next_state_graphs'], next_actions)
                # 适度裁剪目标Q值
                target_q = torch.clamp(target_q, min=-30.0, max=0.0)  # 适度收紧范围
                
                # 使用配置中的折扣因子
                y = critic_batch['reward'] + self.gamma * (1 - critic_batch['dones']) * target_q
            
            current_q = self.critic(critic_batch['state_graphs'], critic_batch['actions'])

            # 计算TD误差（用于更新优先级）
            td_errors = (y - current_q).detach().cpu().numpy().flatten()

            # === 监控指标收集：Q值和TD误差统计 ===
            with torch.no_grad():
                # 收集当前Q值统计
                q_mean = current_q.mean().item()
                q_std = current_q.std().item()
                q_min = current_q.min().item()
                q_max = current_q.max().item()

                # 收集目标Q值统计
                target_q_mean = target_q.mean().item()
                target_q_std = target_q.std().item()
                target_q_min = target_q.min().item()
                target_q_max = target_q.max().item()

                # 收集TD误差统计
                td_mean = np.mean(np.abs(td_errors))
                td_std = np.std(td_errors)
                td_max = np.max(np.abs(td_errors))

            # 使用Huber损失，对异常值更鲁棒
            if weights is not None:
                critic_loss = (weights * F.smooth_l1_loss(current_q, y, reduction='none')).mean()
            else:
                critic_loss = F.smooth_l1_loss(current_q, y)  # 使用Huber损失

            critic_loss_total += critic_loss.item()
            
            # 梯度更新
            self.critic_optimizer.zero_grad()
            (critic_loss * self.critic_loss_weight).backward()

            # === 监控指标收集：Critic梯度范数 ===
            critic_grad_norm = 0.0
            for param in self.critic.parameters():
                if param.grad is not None:
                    critic_grad_norm += param.grad.data.norm(2).item() ** 2
            critic_grad_norm = critic_grad_norm ** 0.5

            # 适度的梯度裁剪
            torch.nn.utils.clip_grad_norm_(self.critic.parameters(), max_norm=0.5)
            self.critic_optimizer.step()
            
            # 更新优先级
            if indices is not None and hasattr(self.replay_buffer, 'update_priorities'):
                self.replay_buffer.update_priorities(indices, td_errors)
        
        # === 2) 更新Actor：使用可微分调节 + 实时监督学习 ===
        actor_loss_value = 0.0  # 默认返回值

        # 根据配置决定Actor更新频率（默认每次都更新，与Critic同步）
        actor_update_ratio = getattr(self, 'actor_update_ratio', 1)
        if self.update_step % actor_update_ratio == 0:
            self.actor_optimizer.zero_grad()

            # 为actor使用独立的batch以避免计算图冲突
            actor_batch = self.replay_buffer.sample(self.batch_size)
            weights = actor_batch.get('weights', None)

            # 🔧 安全的可微分调节：重新生成带梯度的动作
            raw_actions = self.actor(actor_batch['state_graphs'])

            # 尝试使用可微分调节，如果失败则回退到原始动作
            try:
                adjusted_actions = self._safe_differentiable_adjust_batch_actions(raw_actions, actor_batch)
                q_value = self.critic(actor_batch['state_graphs'], adjusted_actions)
                use_adjusted = True
            except Exception as e:
                print(f"警告: 可微分调节失败，使用原始动作: {e}")
                adjusted_actions = raw_actions
                q_value = self.critic(actor_batch['state_graphs'], raw_actions)
                use_adjusted = False

            # 策略损失：使用调节后的动作（如果可用）
            action_for_regularization = adjusted_actions if 'adjusted_actions' in locals() else raw_actions
            if weights is not None:
                policy_loss = -torch.mean(weights * q_value) + 0.0005 * torch.mean(torch.pow(action_for_regularization, 2))
            else:
                policy_loss = -q_value.mean() + 0.0005 * torch.mean(torch.pow(action_for_regularization, 2))

            # 初始化总损失
            total_actor_loss = policy_loss * self.actor_loss_weight

            # === 监督学习损失：优化权重和放大因子 ===
            supervision_loss_val = 0.0

            # === 监督学习损失：使用调节后的动作与CSV最优值比较 ===
            supervision_loss_val = 0.0

            # 启用监督学习
            if self.use_supervision and self.optimal_action_provider is not None:
                supervision_weight = self.get_supervision_weight()

                if 'system_states' in actor_batch and actor_batch['system_states']:
                    optimal_actions = self.optimal_action_provider.get_batch_optimal_actions(actor_batch['system_states'])

                    # 🔧 关键改进：使用当前生成的调节后动作进行监督学习
                    # 这样监督学习的目标与实际执行一致，且保留梯度
                    # adjusted_actions 已经在上面通过可微分调节生成

                    # 使用调节后动作进行监督学习（如果可用）
                    action_for_supervision = adjusted_actions if 'adjusted_actions' in locals() else raw_actions
                    if optimal_actions is not None and optimal_actions.shape == action_for_supervision.shape:
                        # 🔧 关键修复：将调节后动作归一化到[-1,1]范围，与最优动作保持一致
                        # adjusted_actions是MW值，需要归一化后再与optimal_actions比较
                        normalized_action_for_supervision = self._normalize_action_for_storage(action_for_supervision, actor_batch.get('info'))

                        # 使用归一化后的动作进行监督学习
                        supervision_loss = F.mse_loss(normalized_action_for_supervision, optimal_actions)
                        supervision_loss_val = supervision_loss.item()

                        # 累加损失统计
                        self.current_episode_supervision_loss += supervision_loss.item()
                        self.current_episode_policy_loss += policy_loss.item()
                        self.current_episode_update_count += 1

                        # 调试输出（减少频率）
                        if self.episodes_trained % 10 == 0:
                            print(f"Episode {self.episodes_trained}: "
                                  f"监督权重={supervision_weight:.3f}, "
                                  f"策略损失={policy_loss.item():.3f}, "
                                  f"监督损失={supervision_loss.item():.3f}")

                            # 详细调试信息 - 显示归一化修复效果
                            action_type = "调节后动作" if 'adjusted_actions' in locals() and action_for_supervision is adjusted_actions else "原始动作"
                            print(f"  原始{action_type}形状: {action_for_supervision.shape}")
                            print(f"  归一化{action_type}形状: {normalized_action_for_supervision.shape}")
                            print(f"  最优动作形状: {optimal_actions.shape}")
                            print(f"  第一个样本原始{action_type}(MW): {action_for_supervision[0].cpu().detach().numpy()}")
                            print(f"  第一个样本归一化{action_type}: {normalized_action_for_supervision[0].cpu().detach().numpy()}")
                            print(f"  第一个样本最优动作: {optimal_actions[0].cpu().detach().numpy()}")
                            print(f"  🔧 修复效果: 监督损失从数千降低到 {supervision_loss.item():.3f}")

                        # 基于监督损失进行反向传播
                        # 将监督损失添加到总损失中
                        if supervision_weight > 0:
                            total_actor_loss += supervision_weight * supervision_loss
                    else:
                        if optimal_actions is None:
                            print("警告: 无法获取最优动作数据")
                        else:
                            action_type = "调节后动作" if 'adjusted_actions' in locals() and action_for_supervision is adjusted_actions else "原始动作"
                            print(f"警告: 监督学习维度不匹配 - {action_type}: {action_for_supervision.shape}, 最优动作: {optimal_actions.shape}")
                elif self.episodes_trained % 50 == 0:  # 减少警告频率
                    print("警告: 监督学习所需数据不完整，跳过监督学习")

            # 统一进行反向传播
            total_actor_loss.backward()

            # === 监控指标收集：Actor梯度范数 ===
            actor_grad_norm = 0.0
            for param in self.actor.parameters():
                if param.grad is not None:
                    actor_grad_norm += param.grad.data.norm(2).item() ** 2
            actor_grad_norm = actor_grad_norm ** 0.5

            # === 监控指标收集：动作标准差（探索程度） ===
            with torch.no_grad():
                action_std = raw_actions.std().item()

            # 应用梯度裁剪并执行优化器步骤
            torch.nn.utils.clip_grad_norm_(self.actor.parameters(), max_norm=self.grad_clip_norm)
            self.actor_optimizer.step()
            actor_loss_value = total_actor_loss.item() # 修改：返回真正的总Actor损失

        # === 3) 软更新目标网络 ===
        self._update_target(self.tau)

        # === 4) 存储监控指标到历史记录 ===
        # 存储Q值统计
        q_stats = {
            'mean': q_mean, 'std': q_std, 'min': q_min, 'max': q_max
        }
        self.q_value_history.append(q_stats)

        # 存储目标Q值统计
        target_q_stats = {
            'mean': target_q_mean, 'std': target_q_std, 'min': target_q_min, 'max': target_q_max
        }
        self.target_q_history.append(target_q_stats)

        # 存储TD误差统计
        td_stats = {
            'mean': td_mean, 'std': td_std, 'max': td_max
        }
        self.td_error_history.append(td_stats)

        # 存储梯度范数
        self.critic_grad_norm_history.append(critic_grad_norm)

        # 如果更新了Actor，存储Actor相关指标
        if self.update_step % self.actor_update_ratio == 0:  # 根据配置的Actor更新频率
            self.actor_grad_norm_history.append(actor_grad_norm)
            self.action_std_history.append(action_std)
        else:
            # 如果没有更新Actor，存储0值保持数据长度一致
            self.actor_grad_norm_history.append(0.0)
            self.action_std_history.append(0.0)

        # 如果本次未更新Actor，actor_loss_value为0.0；否则为total_actor_loss.item()
        return actor_loss_value, critic_loss_total / critic_update_times
    
    
    def _update_target(self, tau):
        """软更新目标网络"""
        # 使用传入的tau参数而不是固定值，允许更灵活的更新策略
        actual_tau = min(tau, 0.01)  # 限制最大更新率为0.01，避免目标网络变化过快
        
        for t_param, l_param in zip(self.target_actor.parameters(), self.actor.parameters()):
            t_param.data.copy_(actual_tau * l_param.data + (1 - actual_tau) * t_param.data)
        for t_param, l_param in zip(self.target_critic.parameters(), self.critic.parameters()):
            t_param.data.copy_(actual_tau * l_param.data + (1 - actual_tau) * t_param.data)
        
    def update_episode_info(self, episode_reward):
        """更新episode相关信息，调整探索策略"""
        # 保存之前的值用于调试
        prev_episodes = self.episodes_trained
        
        # 记录本回合的平均监督损失和策略损失
        if self.current_episode_update_count > 0:
            avg_supervision_loss = self.current_episode_supervision_loss / self.current_episode_update_count
            avg_policy_loss = self.current_episode_policy_loss / self.current_episode_update_count
        else:
            # 如果本回合没有更新，记录0值而不是使用上一回合的值
            avg_supervision_loss = 0.0
            avg_policy_loss = 0.0
        
        # 【修复】确保每个episode都记录损失，即使没有监督学习
        # 如果没有启用监督学习，记录0值以保持数据长度一致
        if not self.use_supervision:
            avg_supervision_loss = 0.0
        
        # 记录每回合的监督损失、策略损失和权重
        self.supervision_loss_history.append(avg_supervision_loss)
        self.policy_loss_history.append(avg_policy_loss)
        self.supervision_weight_history.append(self.get_supervision_weight())
        
        # 【新增】添加调试信息，确认数据记录
        print(f"【监督学习调试】Episode {self.episodes_trained + 1}: "
              f"监督损失={avg_supervision_loss:.4f}, "
              f"策略损失={avg_policy_loss:.4f}, "
              f"监督权重={self.get_supervision_weight():.4f}, "
              f"更新次数={self.current_episode_update_count}, "
              f"历史长度={len(self.supervision_loss_history)}")
        
        # 重置当前回合的累计值
        self.current_episode_supervision_loss = 0.0
        self.current_episode_policy_loss = 0.0
        self.current_episode_update_count = 0
        
        # 更新回合数
        self.episodes_trained += 1
        
        # 添加详细调试输出
        print(f"【详细调试】update_episode_info - episodes_trained从{prev_episodes}更新为{self.episodes_trained}")
        
        # 调整噪声强度
        self._adjust_noise_scale(episode_reward)
        
        # 使用原始奖励值来判断是否是最佳奖励
        is_best = self.reward_normalizer.is_best_reward(episode_reward)
        if is_best:
            self.best_reward = episode_reward
            self.best_reward_episode = self.episodes_trained
            # 打印调试信息，确认检测到最佳奖励
            print(f"检测到新的最佳奖励: {episode_reward:.2f}")
        
        return is_best
    
    def get_reward_stats(self):
        """获取奖励统计信息"""
        return self.reward_normalizer.get_stats()

    def get_monitoring_stats(self):
        """
        获取关键监控指标的统计信息

        Returns:
            dict: 包含各项监控指标统计的字典
        """
        stats = {}

        # Q值统计
        if self.q_value_history:
            recent_q = self.q_value_history[-10:]  # 最近10次更新
            stats['q_value'] = {
                'recent_mean': np.mean([q['mean'] for q in recent_q]),
                'recent_std': np.mean([q['std'] for q in recent_q]),
                'recent_range': (
                    np.mean([q['min'] for q in recent_q]),
                    np.mean([q['max'] for q in recent_q])
                )
            }

        # 目标Q值统计
        if self.target_q_history:
            recent_target_q = self.target_q_history[-10:]
            stats['target_q_value'] = {
                'recent_mean': np.mean([q['mean'] for q in recent_target_q]),
                'recent_std': np.mean([q['std'] for q in recent_target_q]),
                'recent_range': (
                    np.mean([q['min'] for q in recent_target_q]),
                    np.mean([q['max'] for q in recent_target_q])
                )
            }

        # TD误差统计
        if self.td_error_history:
            recent_td = self.td_error_history[-10:]
            stats['td_error'] = {
                'recent_mean': np.mean([td['mean'] for td in recent_td]),
                'recent_std': np.mean([td['std'] for td in recent_td]),
                'recent_max': np.mean([td['max'] for td in recent_td])
            }

        # 梯度范数统计
        if self.actor_grad_norm_history:
            recent_actor_grad = [g for g in self.actor_grad_norm_history[-10:] if g > 0]  # 排除0值
            if recent_actor_grad:
                stats['actor_grad_norm'] = {
                    'recent_mean': np.mean(recent_actor_grad),
                    'recent_std': np.std(recent_actor_grad),
                    'recent_max': np.max(recent_actor_grad)
                }

        if self.critic_grad_norm_history:
            recent_critic_grad = self.critic_grad_norm_history[-10:]
            stats['critic_grad_norm'] = {
                'recent_mean': np.mean(recent_critic_grad),
                'recent_std': np.std(recent_critic_grad),
                'recent_max': np.max(recent_critic_grad)
            }

        # 动作标准差统计（探索程度）
        if self.action_std_history:
            recent_action_std = [std for std in self.action_std_history[-10:] if std > 0]  # 排除0值
            if recent_action_std:
                stats['action_std'] = {
                    'recent_mean': np.mean(recent_action_std),
                    'recent_std': np.std(recent_action_std),
                    'recent_trend': 'increasing' if len(recent_action_std) > 5 and
                                   np.mean(recent_action_std[-5:]) > np.mean(recent_action_std[:5])
                                   else 'decreasing'
                }

        return stats