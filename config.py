import os
import torch

# 获取项目根目录的绝对路径
ROOT_DIR = os.path.dirname(os.path.abspath(__file__))

# 设备配置
DEVICE = torch.device("cuda" if torch.cuda.is_available() else "cpu")

# 全局调试配置
VERBOSE_OUTPUT = True  # 是否输出详细的调试信息

# 环境配置
ENV_CONFIG = {
    'file_path': os.path.join(ROOT_DIR, 'data', 'active_load_profile.csv'),  # 负荷数据文件
    'gen_schedule_file': os.path.join(ROOT_DIR, 'data', 'power_schedule_with_wind_sequential.csv'),  # 发电机调度数据文件
    'look_ahead': 1,  # 修改为1，实现实时调度
    'debug': VERBOSE_OUTPUT,  # 使用全局调试配置
    'wind_power_enabled': True,   # 启用风力发电

    # 平衡机配置
    'enable_slack_bus': True,  # 是否启用平衡机功能
}

# 智能体配置
AGENT_CONFIG = {
    # === 学习率配置：平衡的学习策略 ===
    'actor_lr': 8e-5,   # 提高Actor学习率，加快学习速度
    'critic_lr': 5e-5,  # 保持Critic学习率

    # === 强化学习基础参数 ===
    'gamma': 0.99,  # 降低折扣因子，更关注即时奖励
    'tau': 0.008,   # 适中的目标网络更新速度
    'buffer_size': 30000,  # 扩大缓冲区，提高样本多样性
    'batch_size': 128,     # 增大批次大小，减少梯度估计方差
    'hidden_size': 192,    # 适中的隐藏层大小，平衡容量与过拟合

    # === 探索策略配置：平衡探索与利用 ===
    'initial_noise_scale': 0.12,  # 适度提高初始噪声，增强早期探索
    'final_noise_scale': 0.05,    # 适度提高最终噪声，保持适度探索
    'exploration_steps': 1200,    # 延长探索期，配合监督学习

    # === 监督学习配置：进一步延长监督学习阶段 ===
    'initial_supervision_weight': 0.6,   # 提高初始监督权重
    'plateau_supervision_weight': 0.4,   # 提高平台期监督权重
    'final_supervision_weight': 0.25,    # 进一步提高最终监督权重

    # 三段式策略比例配置（三个比例之和应为1.0）
    'supervision_stage1_ratio': 0.15,    # 进一步缩短第一阶段
    'supervision_stage2_ratio': 0.7,     # 大幅延长第二阶段（平台期）
    'supervision_stage3_ratio': 0.15,    # 进一步缩短第三阶段

    # === 损失权重与梯度裁剪：平衡训练 ===
    'actor_loss_weight': 0.8,   # 提高Actor损失权重，增强策略学习
    'critic_loss_weight': 0.2,  # 降低Critic损失权重，防止过度拟合
    'grad_clip_norm': 0.8,      # 适中的梯度裁剪，平衡稳定性与学习速度

    # === 网络更新频率配置：新增 ===
    'update_frequency': 4,       # 每4步更新一次网络
    'updates_per_step': 2,       # 每次更新2轮
    'actor_update_ratio': 1,     # Actor每次都更新（与Critic同步）

    'verbose': VERBOSE_OUTPUT,  # 使用全局调试配置
}

# 训练配置
TRAIN_CONFIG = {
    'num_episodes': 800, 
    'verbose': VERBOSE_OUTPUT  # 使用全局调试配置
}

# 测试配置
TEST_CONFIG = {
    'num_episodes': 1,
    'verbose': VERBOSE_OUTPUT  # 使用全局调试配置
}

# 模型保存路径
MODEL_PATHS = {
    'model_dir': os.path.join(ROOT_DIR, 'models'),
    'actor_path': os.path.join(ROOT_DIR, 'models', 'ddpg_actor.pth'),
    'critic_path': os.path.join(ROOT_DIR, 'models', 'ddpg_critic.pth'),
    'best_actor_path': os.path.join(ROOT_DIR, 'models', 'best_actor.pth'),
    'best_critic_path': os.path.join(ROOT_DIR, 'models', 'best_critic.pth')
}

# 图表保存路径
PLOT_CONFIG = {
    'output_dir': os.path.join(ROOT_DIR, 'plots'),
    'reward_plot': 'training_reward_curve.png',
    'loss_plot': 'loss_curves.png',
    'weight_plot': 'supervision_weight_curve.png'
}

# 确保所有配置都能被导出
__all__ = ['ENV_CONFIG', 'AGENT_CONFIG', 'TRAIN_CONFIG', 'MODEL_PATHS', 'PLOT_CONFIG', 'DEVICE', 'VERBOSE_OUTPUT']