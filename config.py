import os
import torch

# 获取项目根目录的绝对路径
ROOT_DIR = os.path.dirname(os.path.abspath(__file__))

# 设备配置
DEVICE = torch.device("cuda" if torch.cuda.is_available() else "cpu")

# 全局调试配置
VERBOSE_OUTPUT = True  # 是否输出详细的调试信息

# 环境配置
ENV_CONFIG = {
    'file_path': os.path.join(ROOT_DIR, 'data', 'active_load_profile.csv'),  # 负荷数据文件
    'gen_schedule_file': os.path.join(ROOT_DIR, 'data', 'power_schedule_with_wind_sequential.csv'),  # 发电机调度数据文件
    'look_ahead': 1,  # 修改为1，实现实时调度
    'debug': VERBOSE_OUTPUT,  # 使用全局调试配置
    'wind_power_enabled': True,   # 启用风力发电

    # 平衡机配置
    'enable_slack_bus': True,  # 是否启用平衡机功能
}

# 智能体配置
AGENT_CONFIG = {
    # === 学习率配置：平衡的学习策略 ===
    'actor_lr': 8e-5,   # 提高Actor学习率，改善学习效果
    'critic_lr': 5e-5,  # 降低Critic学习率，提高稳定性

    # === 强化学习基础参数 ===
    'gamma': 0.99,  # 降低折扣因子，更关注即时奖励
    'tau': 0.01,    # 增大软更新系数，加快目标网络更新
    'buffer_size': 100,  # 适当减小缓冲区，提高样本新鲜度
    'batch_size': 64,     # 增大批次大小，提高梯度估计稳定性
    'hidden_size': 256,    # 保持隐藏层大小

    # === 探索策略配置：优化探索策略 ===
    'initial_noise_scale': 0.1,   # 降低初始噪声强度，减少早期不稳定
    'final_noise_scale': 0.08,     # 提高最终噪声，保持适度探索
    'exploration_steps': 1000,      # 延长探索期到800步

    # === 监督学习配置：灵活的三段式衰减策略 ===
    'initial_supervision_weight': 0.5,   # 初始监督权重
    'plateau_supervision_weight': 0.3,   # 平台期监督权重
    'final_supervision_weight': 0.1,    # 最终监督权重

    # 三段式策略比例配置（三个比例之和应为1.0）
    'supervision_stage1_ratio': 0.3,     # 第一阶段比例（衰减阶段）
    'supervision_stage2_ratio': 0.4,     # 第二阶段比例（平台期）
    'supervision_stage3_ratio': 0.3,     # 第三阶段比例（最终衰减）

    # === 损失权重与梯度裁剪：平衡训练 ===
    'actor_loss_weight': 0.8,   # 提高Actor损失权重，增强策略学习
    'critic_loss_weight': 0.2,  # 降低Critic损失权重，防止过度拟合
    'grad_clip_norm': 1.0,      # 梯度裁剪范数，防止梯度爆炸

    # === 网络更新频率配置：新增 ===
    'update_frequency': 4,       # 每4步更新一次网络
    'updates_per_step': 2,       # 每次更新2轮
    'actor_update_ratio': 1,     # Actor每次都更新（与Critic同步）

    'verbose': VERBOSE_OUTPUT,  # 使用全局调试配置
}

# 训练配置
TRAIN_CONFIG = {
    'num_episodes': 1200, 
    'verbose': VERBOSE_OUTPUT  # 使用全局调试配置
}

# 测试配置
TEST_CONFIG = {
    'num_episodes': 1,
    'verbose': VERBOSE_OUTPUT  # 使用全局调试配置
}

# 模型保存路径
MODEL_PATHS = {
    'model_dir': os.path.join(ROOT_DIR, 'models'),
    'actor_path': os.path.join(ROOT_DIR, 'models', 'ddpg_actor.pth'),
    'critic_path': os.path.join(ROOT_DIR, 'models', 'ddpg_critic.pth'),
    'best_actor_path': os.path.join(ROOT_DIR, 'models', 'best_actor.pth'),
    'best_critic_path': os.path.join(ROOT_DIR, 'models', 'best_critic.pth')
}

# 图表保存路径
PLOT_CONFIG = {
    'output_dir': os.path.join(ROOT_DIR, 'plots'),
    'reward_plot': 'training_reward_curve.png',
    'loss_plot': 'loss_curves.png',
    'weight_plot': 'supervision_weight_curve.png'
}

# 确保所有配置都能被导出
__all__ = ['ENV_CONFIG', 'AGENT_CONFIG', 'TRAIN_CONFIG', 'MODEL_PATHS', 'PLOT_CONFIG', 'DEVICE', 'VERBOSE_OUTPUT']