import numpy as np
import time
import sys
from env.ieee30_env import IEEE30Env
from agents.gnn.gnn_ddpg import GNNDDPGAgent, OptimalActionProvider
from agents.gnn.graph_utils import IEEE30GraphBuilder, state_to_graph
from utils.common import plot_training_curves, save_model, load_model, plot_supervision_curves, plot_monitoring_curves
from config import ENV_CONFIG, AGENT_CONFIG, TRAIN_CONFIG, MODEL_PATHS, PLOT_CONFIG, DEVICE, VERBOSE_OUTPUT
import torch
import os
import datetime
import matplotlib.pyplot as plt
import matplotlib as mpl

# 配置matplotlib支持中文显示
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'KaiTi', 'STFangsong', 'FangSong', 'SimSun', 'FangSongP']  # 按优先级尝试加载中文字体
plt.rcParams['axes.unicode_minus'] = False  # 解决保存图像时负号'-'显示为方块的问题

# 获取项目根目录
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.abspath(current_dir)

# 设置输出不缓冲
sys.stdout.reconfigure(line_buffering=True)  # Python 3.7+

def train(resume=False):
    # 创建日志文件
    log_dir = os.path.join(PLOT_CONFIG['output_dir'], 'logs')
    os.makedirs(log_dir, exist_ok=True)
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    log_file = os.path.join(log_dir, f'training_log_{timestamp}.txt')
    
    start_time = time.time()
    
    # 精简日志输出
    print(f"训练开始时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}", flush=True)
    print(f"使用设备: {DEVICE}", flush=True)
    print(f"环境配置: {ENV_CONFIG}", flush=True)
    print(f"训练配置: {TRAIN_CONFIG}", flush=True)
    print("-" * 50, flush=True)
    
    # 打开日志文件
    with open(log_file, 'w', encoding='utf-8') as f:
        f.write(f"训练开始时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"使用设备: {DEVICE}\n")
        f.write(f"环境配置: {ENV_CONFIG}\n")
        f.write(f"训练配置: {TRAIN_CONFIG}\n")
        f.write("-" * 50 + "\n")

    # 1. 实例化环境
    env = IEEE30Env(**ENV_CONFIG)

    # 2. 根据环境确认维度 - 自动计算状态维度
    state = env.reset()
    state_dim = len(state)  # 动态获取状态维度
    action_dim = env.action_space.shape[0]  # 改为从环境中获取动作维度

    # 在详细模式下显示维度信息
    if VERBOSE_OUTPUT:
        print(f"状态维度: {state_dim}", flush=True)
        print(f"动作维度: {action_dim}", flush=True)
        print(f"预期每个episode运行时间步: 96（一天中每15分钟一个时间点）", flush=True)

    # 3. 初始化图构建器 - 用于将状态转换为图结构
    graph_builder = IEEE30GraphBuilder(
        num_buses=30, 
        num_branches=41, 
        num_gens=6
    )
    
    # 定义节点和边的特征维度
    calculated_node_features = state_dim  # 动态计算节点特征维度
    edge_features = 1  # 支路潮流
    
    # 在详细模式下显示节点特征维度信息
    if VERBOSE_OUTPUT:
        print(f"状态维度: {state_dim}", flush=True)
        print(f"计算得到的每时间步特征维度: {calculated_node_features}", flush=True)
        print(f"实际节点特征维度: 3", flush=True)
    
    # 初始化GNN-DDPG智能体
    agent = GNNDDPGAgent(
        node_features=env.node_features,
        edge_features=env.edge_features,
        action_dim=env.action_space.shape[0],
        action_bound=1.0,
        time_steps=1  # 修改为单步调度
    )
    
    # 显式设置环境的graph_builder给智能体，确保一致性
    agent.env_graph_builder = env.graph_builder
    # 设置环境引用，用于动作归一化
    agent.set_env(env)

    # 加载已有模型
    if resume:
        if load_model(agent, MODEL_PATHS['model_dir']):
            print("[Info] 已加载之前的模型权重")
            # 添加episodes_trained调试信息
            print(f"[调试] 加载模型后的episodes_trained = {agent.episodes_trained}")
    
    # 初始化最优动作提供器
    optimal_action_file = os.path.join(project_root, 'data', 'power_schedule_with_wind_sequential.csv')
    if os.path.exists(optimal_action_file):
        try:
            optimal_action_provider = OptimalActionProvider(optimal_action_file, env)
            agent.set_optimal_action_provider(optimal_action_provider)
            print(f"已加载最优动作数据: {optimal_action_file}", flush=True)
        except Exception as e:
            print(f"加载最优动作数据失败: {e}", flush=True)

    # 初始化各种历史记录列表
    total_penalty_history = []
    normalized_reward_history = []
    actor_loss_history = []
    critic_loss_history = []
    
    # 惩罚项历史记录
    gen_cost_penalty_history = []
    power_balance_penalty_history = []
    gen_limit_penalty_history = []
    line_flow_penalty_history = []
    ramp_penalty_history = []
    
    # 归一化惩罚项历史记录
    norm_gen_cost_history = []
    norm_power_balance_history = []
    norm_gen_limit_history = []
    norm_line_flow_history = []
    norm_ramp_history = []
    
    # 新增：成本比率历史记录
    cost_ratio_history = []
    episode_optimal_costs = []
    episode_actual_costs = []
    
    best_reward = float('-inf')
    
    # 性能监控（仅用于记录，不触发早停）
    best_performance_window = []  # 记录最近的性能
    performance_window_size = 20  # 性能窗口大小
    
    print("开始训练...", flush=True)
    print("=" * 30 + " 关键配置 " + "=" * 30, flush=True)
    print(f"学习率 - Actor: {AGENT_CONFIG['actor_lr']}, Critic: {AGENT_CONFIG['critic_lr']}", flush=True)
    print(f"批次大小: {AGENT_CONFIG['batch_size']}, 软更新系数: {AGENT_CONFIG['tau']}", flush=True)
    print(f"更新频率: 每{AGENT_CONFIG.get('update_frequency', 1)}步更新{AGENT_CONFIG.get('updates_per_step', 1)}次", flush=True)
    print(f"Actor更新比例: 每{AGENT_CONFIG.get('actor_update_ratio', 2)}次Critic更新时更新1次Actor", flush=True)
    print(f"探索策略: {AGENT_CONFIG['initial_noise_scale']} → {AGENT_CONFIG['final_noise_scale']} (over {AGENT_CONFIG['exploration_steps']} episodes)", flush=True)
    print("=" * 80, flush=True)
    
    # 添加奖励统计
    reward_stats = {
        'mean': -float('inf'),
        'std': 0,
        'count': 0,
        'max_reward': -float('inf'),  # 添加历史最大奖励记录
        'max_normalized_reward': -float('inf')  # 添加历史最大归一化奖励记录
    }
    
    # 定义日志输出间隔
    log_interval = 1  # 每个回合都输出日志
    
    # 获取初始监督权重
    supervision_weight = agent.get_supervision_weight() if hasattr(agent, 'get_supervision_weight') else None
    
    for episode in range(TRAIN_CONFIG['num_episodes']):
        # 初始化episode变量
        state = env.reset()
        # 移除episode_reward，只保留归一化奖励累加
        episode_normalized_reward = 0.0  # 累加归一化奖励
        episode_actor_loss = 0.0
        episode_critic_loss = 0.0
        done = False
        episode_steps = 0
        update_times = 0
        
        # 添加变量来追踪整个episode的各项惩罚
        total_gen_cost = 0.0
        total_power_balance = 0.0
        total_branch_violation = 0.0
        total_ramp_violation = 0.0  # 爬坡违约
        total_gen_limit = 0.0  # 发电机越限惩罚
        
        # 用于存储最后一个时间步的状态信息，避免风电值累积问题
        last_step_info = None
        
        # 保存每个时间步的惩罚和风电信息，便于分析累积问题
        step_gen_costs = []
        step_power_balances = []
        step_branch_violations = []
        step_ramp_violations = []  # 爬坡违约
        step_gen_limits = []  # 发电机越限惩罚
        
        # 仅保留必要信息
        step_normalized_rewards = []  # 记录归一化奖励
        step_infos = []
        
        # 添加最大步数限制，确保每个episode运行96个时间步
        max_steps = 96  # 一天中每15分钟一个时间点，共96个时间步
        
        # 在episode开始时调用agent.start_episode()
        agent.start_episode()
        
        # 新增：记录本episode的最优成本和实际成本
        episode_optimal_cost_sum = 0
        episode_actual_cost_sum = 0
        
        while not done and episode_steps < max_steps:
            # 【改进1.4】更平滑的探索-利用权衡：不再使用硬截止，而是根据进度衰减探索概率
            explore_prob = max(0.05, 1.0 - episode / TRAIN_CONFIG['num_episodes'] * 0.95)  # 确保最低5%的探索概率
            add_noise = np.random.random() < explore_prob  # 基于概率决定是否添加噪声
            action = agent.select_action(state, env.graph_builder, add_noise=add_noise)
            
            next_state, reward, done,info = env.step(action)
            if next_state is None:
                next_state = state
                # 只在严重错误时打印警告
                print(f"警告: episode {episode+1} 时间步 {episode_steps} 返回的next_state为None", flush=True)
            
            # 将状态转换为图数据结构
            # 计算期望的node_feature_dim值
            expected_node_feature_dim = state.shape[0] // agent.time_steps
            
            graph_list = state_to_graph(
                state, 
                env.graph_builder, 
                time_steps=agent.time_steps,
                node_feature_dim=expected_node_feature_dim  # 使用动态计算而非固定值
            )
            
            next_graph_list = None
            if not done:
                # 对next_state也使用相同的计算方式
                next_graph_list = state_to_graph(
                    next_state, 
                    env.graph_builder, 
                    time_steps=agent.time_steps,
                    node_feature_dim=expected_node_feature_dim  # 使用动态计算而非固定值
                )
            
            # 存储transition，使用归一化奖励和调节后的动作
            # 如果info中包含adjusted_action，则使用调节后的动作而不是原始动作
            adjusted_action = info.get('adjusted_action', action)
            
            original_reward, normalized_reward = agent.store_transition(graph_list, adjusted_action, reward, next_graph_list if not done else None, done, info)
            
            # 保存每步的信息以便详细分析，但不输出
            step_normalized_rewards.append(normalized_reward.item() if isinstance(normalized_reward, torch.Tensor) else normalized_reward)
            step_infos.append(info)
            
            # 获取归一化的惩罚组件 - 从reward_normalizer获取
            norm_components = agent.reward_normalizer.get_normalized_components() if hasattr(agent, 'reward_normalizer') else None
            if norm_components:
                # 累加归一化惩罚
                if 'gen_cost' in norm_components:
                    step_norm_gen_cost = norm_components['gen_cost']
                    # step_norm_gen_costs.append(step_norm_gen_cost)
                    
                if 'power_balance' in norm_components:
                    step_norm_power_balance = norm_components['power_balance']
                    # step_norm_power_balances.append(step_norm_power_balance)
                    
                if 'branch_violation' in norm_components:
                    step_norm_branch = norm_components['branch_violation']
                    # step_norm_branch_violations.append(step_norm_branch)
                    
                if 'gen_violation' in norm_components:
                    step_norm_ramp = norm_components['gen_violation']
                    # step_norm_ramp_violations.append(step_norm_ramp)
                    
                if 'gen_limit' in norm_components:
                    step_norm_gen_limit = norm_components['gen_limit']
                    # step_norm_gen_limits.append(step_norm_gen_limit)
            
            # 添加调试信息，显示原始动作和调节后的动作（仅在前几个episode）
            if episode < 2 and episode_steps < 3:
                # 确保将张量转换为numpy数组（处理有梯度的张量）
                if hasattr(action, 'cpu') and hasattr(action, 'numpy'):
                    action_np = action.detach().cpu().numpy()
                elif hasattr(action, 'numpy'):
                    action_np = action.detach().numpy()
                else:
                    action_np = np.array(action)
                    
                if hasattr(adjusted_action, 'cpu') and hasattr(adjusted_action, 'numpy'):
                    adj_action_np = adjusted_action.detach().cpu().numpy()
                elif hasattr(adjusted_action, 'numpy'):
                    adj_action_np = adjusted_action.detach().numpy()
                else:
                    adj_action_np = np.array(adjusted_action)
                
                # 格式化为字符串
                orig_action_str = ", ".join([f"{a:.2f}" for a in action_np])
                adj_action_str = ", ".join([f"{a:.2f}" for a in adj_action_np])
                print(f"步骤 {episode_steps}: 原始动作=[{orig_action_str}], 调节后动作=[{adj_action_str}]", flush=True)
            
            # 累加各项惩罚
            if 'reward_components' in info:
                step_reward = info['reward_components']['gen_cost']
                total_gen_cost += step_reward
                step_gen_costs.append(step_reward)
                
                step_reward = info['reward_components']['power_balance']
                total_power_balance += step_reward
                step_power_balances.append(step_reward)
                
                step_reward = info['reward_components']['branch_violation']
                total_branch_violation += step_reward
                step_branch_violations.append(step_reward)
                
                # 处理爬坡惩罚
                step_reward = info['reward_components']['gen_violation'] 
                total_ramp_violation += step_reward  # 将gen_violation视为爬坡惩罚
                step_ramp_violations.append(step_reward)
                
                # 处理发电机越限惩罚
                if 'gen_limit' in info['reward_components']:
                    step_reward = info['reward_components']['gen_limit']
                    total_gen_limit += step_reward
                    step_gen_limits.append(step_reward)
            
            # 保存最后一个时间步的信息，用于显示wind_data
            last_step_info = info
            
            # 只累加归一化奖励，不再累加原始奖励
            episode_normalized_reward += normalized_reward.item() if isinstance(normalized_reward, torch.Tensor) else normalized_reward
            
            # 记录本步骤的最优成本和实际成本
            if 'optimal_cost' in info and info['optimal_cost'] is not None:
                episode_optimal_cost_sum += info['optimal_cost']
                # 实际成本是负的gen_cost_penalty
                if 'reward_components' in info and 'gen_cost' in info['reward_components']:
                    episode_actual_cost_sum += abs(info['reward_components']['gen_cost'])
            
            # 更新网络 - 新策略：每4步更新一次，每次更新2轮
            update_frequency = AGENT_CONFIG.get('update_frequency', 4)
            updates_per_step = AGENT_CONFIG.get('updates_per_step', 2)

            if len(agent.replay_buffer) > agent.batch_size and episode_steps % update_frequency == 0:
                for _ in range(updates_per_step):
                    actor_loss, critic_loss = agent.update()
                    # 只有当损失值不为0时才累加（避免缓冲区不足时的0值影响）
                    if actor_loss > 0 or critic_loss > 0:
                        episode_actor_loss += actor_loss
                        episode_critic_loss += critic_loss
                    
            state = next_state
            episode_steps += 1
            
            # 强制结束逻辑 - 如果到达一天的末尾(第96步)，则结束episode
            if episode_steps >= max_steps:
                done = True
        
        # 在episode结束时调用agent.end_episode()
        agent.end_episode()
        
        # 记录历史数据
        # 确保存储的是标量而不是CUDA张量
        if isinstance(episode_normalized_reward, torch.Tensor):
            episode_normalized_reward = episode_normalized_reward.cpu().item()
        if isinstance(episode_actor_loss, torch.Tensor):
            episode_actor_loss = episode_actor_loss.cpu().item()
        if isinstance(episode_critic_loss, torch.Tensor):
            episode_critic_loss = episode_critic_loss.cpu().item()
            
        # 保持episode_normalized_reward为累积值，不再计算平均值
        # episode_normalized_reward = episode_normalized_reward / episode_steps  # 注释掉这行
        # 不再对损失值计算平均值，保持为总和
        # episode_actor_loss = episode_actor_loss / episode_steps if episode_steps > 0 else 0
        # episode_critic_loss = episode_critic_loss / episode_steps if episode_steps > 0 else 0
        
        # 计算cost_ratio（如果有最优成本数据）
        episode_cost_ratio = None
        if episode_optimal_cost_sum > 0 and episode_actual_cost_sum > 0:
            episode_cost_ratio = episode_optimal_cost_sum / episode_actual_cost_sum
            cost_ratio_history.append(episode_cost_ratio)
            episode_optimal_costs.append(episode_optimal_cost_sum)
            episode_actual_costs.append(episode_actual_cost_sum)
        
        normalized_reward_history.append(episode_normalized_reward)
        actor_loss_history.append(episode_actor_loss)
        critic_loss_history.append(episode_critic_loss)
        
        # 记录各项惩罚
        gen_cost_penalty_history.append(total_gen_cost)
        power_balance_penalty_history.append(total_power_balance)
        gen_limit_penalty_history.append(total_gen_limit)  # 使用发电机越限惩罚
        line_flow_penalty_history.append(total_branch_violation)
        ramp_penalty_history.append(total_ramp_violation)  # 使用爬坡惩罚
        
        # 获取归一化的惩罚组件平均值
        norm_components = agent.reward_normalizer.get_normalized_components() if hasattr(agent, 'reward_normalizer') else None
        if norm_components:
            # 记录归一化惩罚组件（使用最后一步的值作为参考）
            norm_gen_cost_history.append(norm_components.get('gen_cost', 0.0))
            norm_power_balance_history.append(norm_components.get('power_balance', 0.0))
            norm_gen_limit_history.append(norm_components.get('gen_limit', 0.0))
            norm_line_flow_history.append(norm_components.get('branch_violation', 0.0))
            norm_ramp_history.append(norm_components.get('gen_violation', 0.0))
        
        # 更新奖励统计 - 使用total_penalty
        # 计算总惩罚 - 所有惩罚项都是负值，加起来得到负的总惩罚
        # 取消功率平衡惩罚和弃风惩罚
        total_penalty = total_gen_cost + total_branch_violation + total_ramp_violation + total_gen_limit
        # 不再加入功率平衡惩罚和弃风惩罚
        # 风电相关代码已移除

        # 将total_penalty添加到历史记录中
        total_penalty_history.append(total_penalty)
        
        # 使用total_penalty更新统计信息
        reward_stats['count'] += 1
        if reward_stats['count'] == 1:
            reward_stats['mean'] = total_penalty
            reward_stats['std'] = 1e-8
        else:
            old_mean = reward_stats['mean']
            reward_stats['mean'] += (total_penalty - old_mean) / reward_stats['count']
            reward_stats['std'] = np.sqrt(
                (reward_stats['std']**2 * (reward_stats['count']-2) + 
                (total_penalty - old_mean) * (total_penalty - reward_stats['mean'])) / 
                (reward_stats['count']-1)
            )
                
        # 检查是否出现新的最大奖励 - 更高的值表示更好的性能
        is_new_max = False
        if total_penalty > reward_stats['max_reward']:
            is_new_max = True
            reward_stats['max_reward'] = total_penalty
            reward_stats['max_normalized_reward'] = episode_normalized_reward
            best_reward = total_penalty  # 更新best_reward变量

        # 记录训练信息 - 显示Actor损失和归一化奖励
        # 修改注释和打印内容
        log_msg = f"Episode {episode+1}/{TRAIN_CONFIG['num_episodes']} | " \
                 f"原始奖励: {total_penalty:.2f} | " \
                 f"归一化奖励: {episode_normalized_reward:.4f} | " \
                 f"Actor总损失: {episode_actor_loss:.4f} | " \
                 f"Critic总损失: {episode_critic_loss:.4f}"
        
        # 风电代码已移除
        
        # 记录详细的惩罚信息
        detail_msg = f"  发电成本总和: {total_gen_cost:.2f}, " \
                    f"功率平衡惩罚总和: {total_power_balance:.2f} (已取消), " \
                    f"线路过载惩罚总和: {total_branch_violation:.2f}, " \
                    f"爬坡限制惩罚总和: {total_ramp_violation:.2f}, " \
                    f"发电机越限惩罚总和: {total_gen_limit:.2f}"
            
        # 计算进度并更新进度条
        progress = (episode + 1) / TRAIN_CONFIG['num_episodes'] * 100
        
        # 更新日志输出，显示累积归一化奖励
        if episode % log_interval == 0 or episode == TRAIN_CONFIG['num_episodes'] - 1:
            timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

            # 获取监督损失值、策略损失值和监督权重
            episode_supervision_loss = 0.0
            episode_policy_loss = 0.0
            supervision_weight = None
            if hasattr(agent, 'supervision_loss_history') and len(agent.supervision_loss_history) > 0:
                episode_supervision_loss = agent.supervision_loss_history[-1]
            if hasattr(agent, 'policy_loss_history') and len(agent.policy_loss_history) > 0:
                episode_policy_loss = agent.policy_loss_history[-1]
            if hasattr(agent, 'get_supervision_weight'):
                supervision_weight = agent.get_supervision_weight()

            # === 新增：获取关键监控指标 ===
            monitoring_stats = agent.get_monitoring_stats() if hasattr(agent, 'get_monitoring_stats') else {}

            # 构建日志消息 - 修改：Actor总损失现在是真正的总损失，新增策略损失字段
            log_message = (
                f"{timestamp} | 回合: {episode+1}/{TRAIN_CONFIG['num_episodes']} ({progress:.1f}%) | "
                f"步数: {episode_steps} | "
                f"总奖励: {total_penalty:.2f} | "
                f"归一化奖励: {episode_normalized_reward:.4f} | "
                f"Actor总损失: {episode_actor_loss:.4f} | "
                f"策略损失: {episode_policy_loss:.4f} | "
                f"Critic总损失: {episode_critic_loss:.4f} | "
                f"监督损失: {episode_supervision_loss:.4f}"
            )

            # === 新增：添加关键监控指标到日志 ===
            if monitoring_stats:
                # Q值信息
                if 'q_value' in monitoring_stats:
                    q_stats = monitoring_stats['q_value']
                    log_message += f" | Q值: {q_stats['recent_mean']:.2f}±{q_stats['recent_std']:.2f}"

                # TD误差信息
                if 'td_error' in monitoring_stats:
                    td_stats = monitoring_stats['td_error']
                    log_message += f" | TD误差: {td_stats['recent_mean']:.3f}"

                # 梯度范数信息
                if 'critic_grad_norm' in monitoring_stats:
                    critic_grad = monitoring_stats['critic_grad_norm']
                    log_message += f" | Critic梯度: {critic_grad['recent_mean']:.3f}"

                if 'actor_grad_norm' in monitoring_stats:
                    actor_grad = monitoring_stats['actor_grad_norm']
                    log_message += f" | Actor梯度: {actor_grad['recent_mean']:.3f}"

                # 探索程度信息
                if 'action_std' in monitoring_stats:
                    action_stats = monitoring_stats['action_std']
                    log_message += f" | 动作标准差: {action_stats['recent_mean']:.3f}"

            # 添加额外信息
            if supervision_weight is not None:
                log_message += f" | 监督权重: {supervision_weight:.3f}"
                
            if episode_cost_ratio is not None:
                log_message += f" | 成本比: {episode_cost_ratio:.4f}"
            
            # 输出到控制台和日志文件
            print(log_message, flush=True)
        
            # 将日志写入文件
        with open(log_file, 'a', encoding='utf-8') as f:
                f.write(log_message + '\n')
                # 添加惩罚组件详细信息到日志文件
                f.write(detail_msg + '\n')
        
        # 如果出现新的最大奖励，显示醒目提示
        if is_new_max:
            # 不再显示风电信息
            max_reward_msg = "\n" + "=" * 80 + "\n" + \
                           f"🌟 新的最大奖励! 🌟\n" + \
                           f"原始奖励: {total_penalty:.2f} (提升: {total_penalty - best_reward:.2f})\n" + \
                           f"归一化奖励: {episode_normalized_reward:.4f}\n" + \
                           f"第 {episode+1} 回合\n" + \
                           f"惩罚组件详情:\n{detail_msg}\n" + \
                           "=" * 80 + "\n"
            # 终端和日志文件都显示最大奖励信息
            print(max_reward_msg, flush=True)
            with open(log_file, 'a', encoding='utf-8') as f:
                f.write(max_reward_msg)
        
        # 更新episode信息，用于自适应学习率和tau，使用total_penalty判断是否为最佳模型
        is_best = agent.update_episode_info(total_penalty)
        if is_best:
            best_reward = total_penalty
            # 终端和日志文件都显示新的最佳奖励信息
            best_msg = f"===== 新的最佳奖励 - 原始奖励: {total_penalty:.2f}, 归一化奖励: {episode_normalized_reward:.4f} ====="
            print(best_msg, flush=True)
            with open(log_file, 'a', encoding='utf-8') as f:
                f.write(best_msg + "\n")
            save_model(agent, MODEL_PATHS['model_dir'], is_best=True)
        
        # 在每个episode结束时更新监督权重
        if hasattr(agent, 'get_supervision_weight'):
            supervision_weight = agent.get_supervision_weight()
            # 添加episodes_trained的调试输出
            print(f"【调试信息】当前episodes_trained = {agent.episodes_trained}, 监督权重 = {supervision_weight:.4f}", flush=True)
        
        # 向控制台输出各项惩罚详情
        print(detail_msg, flush=True)
        
        # === 性能监控（仅记录，不触发早停） ===
        # 更新性能窗口用于统计分析
        best_performance_window.append(total_penalty)
        if len(best_performance_window) > performance_window_size:
            best_performance_window.pop(0)
        
        # 显示性能改善信息（仅提示，不触发任何操作）
        if len(best_performance_window) >= performance_window_size:
            current_avg_performance = np.mean(best_performance_window)
            if total_penalty > best_reward:
                print(f"🚀 性能改善! 当前: {total_penalty:.2f}, 历史最佳: {best_reward:.2f}", flush=True)
        
        # 终端显示分隔符
        print("-" * 50, flush=True)
        with open(log_file, 'a', encoding='utf-8') as f:
            f.write("-" * 50 + "\n")

    # 保存最终模型
    save_model(agent, MODEL_PATHS['model_dir'])
    print("-" * 50, flush=True)
    print("训练完成", flush=True)

    # 绘制训练曲线
    # 确保所有数据都是CPU上的数据而不是GPU张量
    # 在创建NumPy数组前检查并将CUDA张量移至CPU
    def to_cpu_numpy(data_list):
        cpu_list = []
        for item in data_list:
            if isinstance(item, torch.Tensor):
                cpu_list.append(item.cpu().numpy())
            elif item is None:
                # 处理None值，将其转换为NaN
                cpu_list.append(np.nan)
            else:
                cpu_list.append(item)
        return np.array(cpu_list, dtype=np.float32)
    
    total_penalty_np = to_cpu_numpy(total_penalty_history)
    normalized_reward_history_np = to_cpu_numpy(normalized_reward_history)  # 添加归一化奖励
    actor_loss_history_np = to_cpu_numpy(actor_loss_history)
    critic_loss_history_np = to_cpu_numpy(critic_loss_history)
    
    # 转换cost_ratio_history为NumPy数组
    cost_ratio_np = to_cpu_numpy(cost_ratio_history) if cost_ratio_history else None
    
    # 创建惩罚的字典，用于绘图
    penalties_data = {
        'gen_cost_penalty': to_cpu_numpy(gen_cost_penalty_history),
        'power_balance_penalty': to_cpu_numpy(power_balance_penalty_history),
        'gen_limit_penalty': to_cpu_numpy(gen_limit_penalty_history),
        'line_flow_penalty': to_cpu_numpy(line_flow_penalty_history),
        'ramp_penalty': to_cpu_numpy(ramp_penalty_history),
        'normalized_reward': to_cpu_numpy(normalized_reward_history),  # 添加归一化奖励数据
        # 添加归一化惩罚组件数据
        'norm_gen_cost': to_cpu_numpy(norm_gen_cost_history),
        'norm_power_balance': to_cpu_numpy(norm_power_balance_history),
        'norm_gen_limit': to_cpu_numpy(norm_gen_limit_history),
        'norm_line_flow': to_cpu_numpy(norm_line_flow_history),
        'norm_ramp': to_cpu_numpy(norm_ramp_history),
    }
    
    # 绘制cost_ratio曲线
    if cost_ratio_np is not None:
        plt.figure(figsize=(14, 7))
        plt.plot(range(1, len(cost_ratio_np) + 1), cost_ratio_np, label='成本比率', color='green')
        plt.xlabel('训练回合')
        plt.ylabel('成本比率 (最优成本/实际成本)')
        plt.title('成本比率曲线')
        plt.legend(prop={'family': 'SimHei'})  # 设置图例字体
        plt.grid(True)
        plt.savefig(os.path.join(PLOT_CONFIG['output_dir'], 'Cost_Ratio.png'))
        plt.close()
        
        # 记录cost_ratio相关信息
        with open(log_file, 'a', encoding='utf-8') as f:
            f.write("\n" + "-" * 50 + "\n")
            f.write("成本比率统计:\n")
            f.write(f"最终成本比率: {cost_ratio_np[-1]:.4f}\n")
            f.write(f"平均成本比率(最后10次): {np.mean(cost_ratio_np[-10:]):.4f}\n")
            f.write(f"最高成本比率: {max(cost_ratio_np):.4f}\n")
            f.write(f"最低成本比率: {min(cost_ratio_np):.4f}\n")
            f.write(f"平均成本比率: {np.mean(cost_ratio_np):.4f}\n")
    
    plot_training_curves(
        total_penalty_np,  # 使用total_penalty来绘图
        actor_loss_history_np, 
        critic_loss_history_np,
        PLOT_CONFIG['output_dir'],
        penalties_data  # 添加惩罚数据
    )
    
    # 绘制监督学习相关的曲线
    if agent.use_supervision and hasattr(agent, 'supervision_loss_history'):
        # 确保所有历史数据都已转换为NumPy数组
        supervision_loss_np = to_cpu_numpy(agent.supervision_loss_history)
        policy_loss_np = to_cpu_numpy(agent.policy_loss_history)
        supervision_weight_np = to_cpu_numpy(agent.supervision_weight_history)
        
        plot_supervision_curves(
            supervision_loss_np,
            policy_loss_np,
            supervision_weight_np,
            PLOT_CONFIG['output_dir']
        )
        print(f"已绘制监督学习相关曲线，共{len(supervision_loss_np)}个数据点", flush=True)

        # 将监督学习相关数据记录到日志文件
        with open(log_file, 'a', encoding='utf-8') as f:
            f.write("\n" + "-" * 50 + "\n")
            f.write("监督学习统计:\n")
            if len(supervision_loss_np) > 0:
                f.write(f"最终监督损失: {supervision_loss_np[-1]:.4f}\n")
                f.write(f"平均监督损失(最后10次): {np.mean(supervision_loss_np[-10:]):.4f}\n")
                f.write(f"最大监督损失: {np.max(supervision_loss_np):.4f}\n")
                f.write(f"最小监督损失: {np.min(supervision_loss_np):.4f}\n")
            if len(policy_loss_np) > 0:
                f.write(f"最终策略损失: {policy_loss_np[-1]:.4f}\n")
                f.write(f"平均策略损失(最后10次): {np.mean(policy_loss_np[-10:]):.4f}\n")
            if len(supervision_weight_np) > 0:
                f.write(f"最终监督权重: {supervision_weight_np[-1]:.4f}\n")
                f.write(f"初始监督权重: {supervision_weight_np[0]:.4f}\n")

    # === 新增：绘制关键监控指标曲线 ===
    print("正在绘制关键监控指标曲线...", flush=True)
    plot_monitoring_curves(agent, PLOT_CONFIG['output_dir'])

    # 将监控指标统计记录到日志文件
    final_monitoring_stats = agent.get_monitoring_stats() if hasattr(agent, 'get_monitoring_stats') else {}
    if final_monitoring_stats:
        with open(log_file, 'a', encoding='utf-8') as f:
            f.write("\n" + "-" * 50 + "\n")
            f.write("关键监控指标统计:\n")

            if 'q_value' in final_monitoring_stats:
                q_stats = final_monitoring_stats['q_value']
                f.write(f"Q值 - 均值: {q_stats['recent_mean']:.3f}, 标准差: {q_stats['recent_std']:.3f}\n")
                f.write(f"Q值 - 范围: [{q_stats['recent_range'][0]:.3f}, {q_stats['recent_range'][1]:.3f}]\n")

            if 'td_error' in final_monitoring_stats:
                td_stats = final_monitoring_stats['td_error']
                f.write(f"TD误差 - 均值: {td_stats['recent_mean']:.4f}, 最大值: {td_stats['recent_max']:.4f}\n")

            if 'critic_grad_norm' in final_monitoring_stats:
                critic_grad = final_monitoring_stats['critic_grad_norm']
                f.write(f"Critic梯度范数 - 均值: {critic_grad['recent_mean']:.4f}, 最大值: {critic_grad['recent_max']:.4f}\n")

            if 'actor_grad_norm' in final_monitoring_stats:
                actor_grad = final_monitoring_stats['actor_grad_norm']
                f.write(f"Actor梯度范数 - 均值: {actor_grad['recent_mean']:.4f}, 最大值: {actor_grad['recent_max']:.4f}\n")

            if 'action_std' in final_monitoring_stats:
                action_stats = final_monitoring_stats['action_std']
                f.write(f"动作标准差 - 均值: {action_stats['recent_mean']:.4f}, 趋势: {action_stats['recent_trend']}\n")
    
    end_time = time.time()
    training_time = (end_time - start_time) / 3600
    
    print(f"训练总耗时: {training_time:.2f} 小时", flush=True)
    
    # 在日志文件中记录训练总结
    with open(log_file, 'a', encoding='utf-8') as f:
        f.write("\n" + "-" * 50 + "\n")
        f.write(f"训练结束时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"训练总耗时: {training_time:.2f} 小时\n")
        f.write(f"最佳奖励: {best_reward:.2f}\n")
        f.write(f"最终原始奖励: {total_penalty_history[-1]:.2f}\n")  # 使用total_penalty_history
        f.write(f"最终归一化奖励: {normalized_reward_history[-1]:.4f}\n")
        f.write(f"平均原始奖励(最后10次): {np.mean(total_penalty_history[-10:]):.2f}\n")  # 使用total_penalty_history
        f.write(f"归一化奖励(最后10次平均): {np.mean(normalized_reward_history[-10:]):.4f}\n")
        
    print(f"训练日志已保存到: {log_file}", flush=True)

def evaluate(env, agent, num_episodes=5):
    """评估函数"""
    total_reward = 0
    
    for _ in range(num_episodes):
        state = env.reset()
        done = False
        episode_total_penalty = 0
        
        # 惩罚组件累计
        total_gen_cost = 0.0
        total_power_balance = 0.0
        total_branch_violation = 0.0 
        total_ramp_violation = 0.0  # 爬坡违约
        total_gen_limit = 0.0  # 发电机越限惩罚
        
        while not done:
            action = agent.select_action(state, env.graph_builder, add_noise=False)
            next_state, _, done, info = env.step(action)
            
            # 累加各项惩罚
            if 'reward_components' in info:
                total_gen_cost += info['reward_components']['gen_cost']
                total_power_balance += info['reward_components']['power_balance']
                total_branch_violation += info['reward_components']['branch_violation']
                total_ramp_violation += info['reward_components']['gen_violation']  # 将gen_violation视为爬坡惩罚
                if 'gen_limit' in info['reward_components']:
                    total_gen_limit += info['reward_components']['gen_limit']  # 发电机越限惩罚
                
            # 风电相关代码已移除
                
            state = next_state
            
        # 计算总惩罚
        # 取消功率平衡惩罚和弃风惩罚
        episode_total_penalty = total_gen_cost + total_branch_violation + total_ramp_violation + total_gen_limit
        # 不再加入功率平衡惩罚和弃风惩罚
        # 风电相关代码已移除
            
        total_reward += episode_total_penalty
    
    return total_reward / num_episodes

if __name__ == "__main__":
    train()