from pypower.api import runpf
# 如果你要单独使用 case30，可以直接从 pypower.case30 导入：
from pypower.case30 import case30

def main():
    # 1. 获取 IEEE 30-bus 测试系统的 ppc (PyPower case)
    ppc = case30()
    
    # 2. 运行潮流计算
    solved_ppc, success = runpf(ppc)
    
    # 3. 查看是否成功收敛并打印相关结果
    if success:
        print("潮流计算收敛！")
        # 例如查看潮流结果中的发电机有功功率
        print("已计算潮流后的发电机信息：")
        print(solved_ppc['gen'])
    else:
        print("潮流计算未能收敛，请检查系统或初始数据。")

if __name__ == "__main__":
    main() 